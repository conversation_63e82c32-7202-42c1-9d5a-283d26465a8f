'use client';

import React, { useState, useEffect } from 'react';
import Header from './components/Header';
import CitySearchInput from './components/CitySearchInput';
import TimeZoneList from './components/TimeZoneList';
import QuickAddTimezones from './components/QuickAddTimezones';
import { useTheme, useTimeFormat, useTimeZones } from './hooks/useLocalStorage';
import { getUserTimezone } from './utils/time';
import { getTimezoneByIdentifier } from './utils/timezones';

export default function Home() {
  const [theme, setTheme] = useTheme();
  const [timeFormat, setTimeFormat] = useTimeFormat();
  const { timeZones, addTimeZone, removeTimeZone } = useTimeZones();
  const [referenceTime, setReferenceTime] = useState<Date | null>(null);
  const [showTimeDifference, setShowTimeDifference] = useState(true);
  const [userTimeZone] = useState(() => getUserTimezone());

  // Initialize with user's local timezone on first load
  useEffect(() => {
    if (timeZones.length === 0) {
      const userTimezone = getUserTimezone();
      const localCity = getTimezoneByIdentifier(userTimezone);

      if (localCity) {
        addTimeZone({
          ...localCity,
          id: `local-${Date.now()}`
        });
      } else {
        // Fallback: add a generic local timezone entry
        addTimeZone({
          city: 'Local Time',
          timeZone: userTimezone,
          id: `local-${Date.now()}`
        });
      }
    }
  }, [timeZones.length, addTimeZone]);

  const handleCitySelect = (city: { city: string; timeZone: string; id: string }) => {
    addTimeZone(city);
  };

  const handleTimeChange = (newTime: Date) => {
    setReferenceTime(newTime);
  };

  const handleRemoveTimeZone = (id: string) => {
    removeTimeZone(id);
    // Reset reference time when removing a timezone
    if (referenceTime) {
      setReferenceTime(null);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header
        timeFormat={timeFormat}
        onTimeFormatChange={setTimeFormat}
        theme={theme}
        onThemeChange={setTheme}
      />

      <main className="max-w-6xl mx-auto px-4 py-8">
        <div className="space-y-8">
          {/* Search Input */}
          <div className="flex justify-center">
            <CitySearchInput onCitySelect={handleCitySelect} />
          </div>

          {/* Quick Add Popular Timezones */}
          {timeZones.length < 6 && (
            <div className="max-w-2xl mx-auto">
              <QuickAddTimezones
                onCitySelect={handleCitySelect}
                existingTimezones={timeZones.map(tz => tz.timeZone)}
              />
            </div>
          )}

          {/* Controls */}
          {timeZones.length > 1 && (
            <div className="flex justify-center">
              <button
                onClick={() => setShowTimeDifference(!showTimeDifference)}
                className="flex items-center gap-2 px-4 py-2 bg-secondary text-secondary-foreground rounded-lg hover:bg-accent transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {showTimeDifference ? 'Hide' : 'Show'} time differences
              </button>
            </div>
          )}

          {/* Time Zone Cards */}
          <TimeZoneList
            timeZones={timeZones}
            timeFormat={timeFormat}
            referenceTime={referenceTime}
            onTimeChange={handleTimeChange}
            onRemoveTimeZone={handleRemoveTimeZone}
            showTimeDifference={showTimeDifference}
            userTimeZone={userTimeZone}
          />
        </div>
      </main>

      <footer className="mt-16 py-8 border-t border-border">
        <div className="max-w-6xl mx-auto px-4 text-center">
          <p className="text-muted-foreground text-sm">
            Built with ❤️ by{' '}
            <a
              href="https://github.com/chirag127"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline"
            >
              Chirag Singhal
            </a>
            {' '}• Open source on{' '}
            <a
              href="https://github.com/chirag127/Time-Zone-Converter"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline"
            >
              GitHub
            </a>
          </p>
        </div>
      </footer>
    </div>
  );
}
