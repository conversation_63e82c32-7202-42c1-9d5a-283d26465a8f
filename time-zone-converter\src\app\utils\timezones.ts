/**
 * Comprehensive list of world cities and their corresponding IANA time zone names
 * This data is used for the city search autocomplete functionality
 */
export const timeZones = [
  // North America
  { city: 'New York', timeZone: 'America/New_York' },
  { city: 'Los Angeles', timeZone: 'America/Los_Angeles' },
  { city: 'Chicago', timeZone: 'America/Chicago' },
  { city: 'Denver', timeZone: 'America/Denver' },
  { city: 'Phoenix', timeZone: 'America/Phoenix' },
  { city: 'Las Vegas', timeZone: 'America/Los_Angeles' },
  { city: 'Miami', timeZone: 'America/New_York' },
  { city: 'Seattle', timeZone: 'America/Los_Angeles' },
  { city: 'Boston', timeZone: 'America/New_York' },
  { city: 'San Francisco', timeZone: 'America/Los_Angeles' },
  { city: 'Washington DC', timeZone: 'America/New_York' },
  { city: 'Atlanta', timeZone: 'America/New_York' },
  { city: 'Dallas', timeZone: 'America/Chicago' },
  { city: 'Houston', timeZone: 'America/Chicago' },
  { city: 'Philadelphia', timeZone: 'America/New_York' },
  { city: 'Toronto', timeZone: 'America/Toronto' },
  { city: 'Vancouver', timeZone: 'America/Vancouver' },
  { city: 'Montreal', timeZone: 'America/Montreal' },
  { city: 'Calgary', timeZone: 'America/Edmonton' },
  { city: 'Mexico City', timeZone: 'America/Mexico_City' },
  { city: 'Anchorage', timeZone: 'America/Anchorage' },
  { city: 'Honolulu', timeZone: 'Pacific/Honolulu' },

  // Europe
  { city: 'London', timeZone: 'Europe/London' },
  { city: 'Paris', timeZone: 'Europe/Paris' },
  { city: 'Berlin', timeZone: 'Europe/Berlin' },
  { city: 'Rome', timeZone: 'Europe/Rome' },
  { city: 'Madrid', timeZone: 'Europe/Madrid' },
  { city: 'Amsterdam', timeZone: 'Europe/Amsterdam' },
  { city: 'Brussels', timeZone: 'Europe/Brussels' },
  { city: 'Vienna', timeZone: 'Europe/Vienna' },
  { city: 'Zurich', timeZone: 'Europe/Zurich' },
  { city: 'Stockholm', timeZone: 'Europe/Stockholm' },
  { city: 'Oslo', timeZone: 'Europe/Oslo' },
  { city: 'Copenhagen', timeZone: 'Europe/Copenhagen' },
  { city: 'Helsinki', timeZone: 'Europe/Helsinki' },
  { city: 'Warsaw', timeZone: 'Europe/Warsaw' },
  { city: 'Prague', timeZone: 'Europe/Prague' },
  { city: 'Budapest', timeZone: 'Europe/Budapest' },
  { city: 'Athens', timeZone: 'Europe/Athens' },
  { city: 'Istanbul', timeZone: 'Europe/Istanbul' },
  { city: 'Moscow', timeZone: 'Europe/Moscow' },
  { city: 'Dublin', timeZone: 'Europe/Dublin' },
  { city: 'Lisbon', timeZone: 'Europe/Lisbon' },
  { city: 'Barcelona', timeZone: 'Europe/Madrid' },
  { city: 'Munich', timeZone: 'Europe/Berlin' },
  { city: 'Milan', timeZone: 'Europe/Rome' },

  // Asia
  { city: 'Tokyo', timeZone: 'Asia/Tokyo' },
  { city: 'Beijing', timeZone: 'Asia/Shanghai' },
  { city: 'Shanghai', timeZone: 'Asia/Shanghai' },
  { city: 'Hong Kong', timeZone: 'Asia/Hong_Kong' },
  { city: 'Singapore', timeZone: 'Asia/Singapore' },
  { city: 'Seoul', timeZone: 'Asia/Seoul' },
  { city: 'Mumbai', timeZone: 'Asia/Kolkata' },
  { city: 'Delhi', timeZone: 'Asia/Kolkata' },
  { city: 'Bangalore', timeZone: 'Asia/Kolkata' },
  { city: 'Bangkok', timeZone: 'Asia/Bangkok' },
  { city: 'Jakarta', timeZone: 'Asia/Jakarta' },
  { city: 'Manila', timeZone: 'Asia/Manila' },
  { city: 'Kuala Lumpur', timeZone: 'Asia/Kuala_Lumpur' },
  { city: 'Dubai', timeZone: 'Asia/Dubai' },
  { city: 'Tel Aviv', timeZone: 'Asia/Jerusalem' },
  { city: 'Riyadh', timeZone: 'Asia/Riyadh' },
  { city: 'Doha', timeZone: 'Asia/Qatar' },
  { city: 'Kuwait City', timeZone: 'Asia/Kuwait' },
  { city: 'Taipei', timeZone: 'Asia/Taipei' },
  { city: 'Ho Chi Minh City', timeZone: 'Asia/Ho_Chi_Minh' },
  { city: 'Hanoi', timeZone: 'Asia/Ho_Chi_Minh' },
  { city: 'Yangon', timeZone: 'Asia/Yangon' },
  { city: 'Dhaka', timeZone: 'Asia/Dhaka' },
  { city: 'Karachi', timeZone: 'Asia/Karachi' },
  { city: 'Islamabad', timeZone: 'Asia/Karachi' },

  // Australia & Oceania
  { city: 'Sydney', timeZone: 'Australia/Sydney' },
  { city: 'Melbourne', timeZone: 'Australia/Melbourne' },
  { city: 'Brisbane', timeZone: 'Australia/Brisbane' },
  { city: 'Perth', timeZone: 'Australia/Perth' },
  { city: 'Adelaide', timeZone: 'Australia/Adelaide' },
  { city: 'Auckland', timeZone: 'Pacific/Auckland' },
  { city: 'Wellington', timeZone: 'Pacific/Auckland' },
  { city: 'Fiji', timeZone: 'Pacific/Fiji' },

  // South America
  { city: 'São Paulo', timeZone: 'America/Sao_Paulo' },
  { city: 'Rio de Janeiro', timeZone: 'America/Sao_Paulo' },
  { city: 'Buenos Aires', timeZone: 'America/Argentina/Buenos_Aires' },
  { city: 'Santiago', timeZone: 'America/Santiago' },
  { city: 'Lima', timeZone: 'America/Lima' },
  { city: 'Bogotá', timeZone: 'America/Bogota' },
  { city: 'Caracas', timeZone: 'America/Caracas' },
  { city: 'Montevideo', timeZone: 'America/Montevideo' },

  // Africa
  { city: 'Cairo', timeZone: 'Africa/Cairo' },
  { city: 'Lagos', timeZone: 'Africa/Lagos' },
  { city: 'Johannesburg', timeZone: 'Africa/Johannesburg' },
  { city: 'Cape Town', timeZone: 'Africa/Johannesburg' },
  { city: 'Nairobi', timeZone: 'Africa/Nairobi' },
  { city: 'Casablanca', timeZone: 'Africa/Casablanca' },
  { city: 'Tunis', timeZone: 'Africa/Tunis' },
  { city: 'Algiers', timeZone: 'Africa/Algiers' },
  { city: 'Addis Ababa', timeZone: 'Africa/Addis_Ababa' },
  { city: 'Accra', timeZone: 'Africa/Accra' },

  // Additional major cities
  { city: 'Reykjavik', timeZone: 'Atlantic/Reykjavik' },
  { city: 'Azores', timeZone: 'Atlantic/Azores' },
  { city: 'Canary Islands', timeZone: 'Atlantic/Canary' },
  { city: 'Bermuda', timeZone: 'Atlantic/Bermuda' },
  { city: 'Barbados', timeZone: 'America/Barbados' },
  { city: 'Jamaica', timeZone: 'America/Jamaica' },
  { city: 'Puerto Rico', timeZone: 'America/Puerto_Rico' },
  { city: 'Guam', timeZone: 'Pacific/Guam' },
  { city: 'Samoa', timeZone: 'Pacific/Apia' },
  { city: 'Tahiti', timeZone: 'Pacific/Tahiti' },
  { city: 'Tonga', timeZone: 'Pacific/Tongatapu' },
  { city: 'Vanuatu', timeZone: 'Pacific/Efate' },
  { city: 'New Caledonia', timeZone: 'Pacific/Noumea' },
  { city: 'Solomon Islands', timeZone: 'Pacific/Guadalcanal' },
  { city: 'Marshall Islands', timeZone: 'Pacific/Majuro' },
  { city: 'Palau', timeZone: 'Pacific/Palau' },
  { city: 'Micronesia', timeZone: 'Pacific/Chuuk' },
  { city: 'Kiribati', timeZone: 'Pacific/Tarawa' },
  { city: 'Cook Islands', timeZone: 'Pacific/Rarotonga' },
  { city: 'Easter Island', timeZone: 'Pacific/Easter' },
  { city: 'Galapagos', timeZone: 'Pacific/Galapagos' },
  { city: 'Marquesas', timeZone: 'Pacific/Marquesas' },
  { city: 'Pitcairn', timeZone: 'Pacific/Pitcairn' },
  { city: 'Norfolk Island', timeZone: 'Pacific/Norfolk' },
  { city: 'Lord Howe Island', timeZone: 'Australia/Lord_Howe' },
  { city: 'Chatham Islands', timeZone: 'Pacific/Chatham' }
];

/**
 * Get timezone data by timezone identifier
 */
export function getTimezoneByIdentifier(timeZone: string) {
  return timeZones.find(tz => tz.timeZone === timeZone);
}

/**
 * Search timezones by city name or timezone identifier
 */
export function searchTimezones(query: string, limit: number = 10) {
  const lowercaseQuery = query.toLowerCase();
  return timeZones
    .filter(tz => 
      tz.city.toLowerCase().includes(lowercaseQuery) ||
      tz.timeZone.toLowerCase().includes(lowercaseQuery)
    )
    .slice(0, limit);
}
