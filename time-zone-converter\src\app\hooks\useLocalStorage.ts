'use client';

import { useState, useEffect } from 'react';

/**
 * Custom hook for managing localStorage with React state
 * Provides automatic synchronization between localStorage and component state
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void] {
  // State to store our value
  // Pass initial state function to useState so logic is only executed once
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }
    
    try {
      // Get from local storage by key
      const item = window.localStorage.getItem(key);
      // Parse stored json or if none return initialValue
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      // If error also return initialValue
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Return a wrapped version of useState's setter function that ...
  // ... persists the new value to localStorage.
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      
      // Save state
      setStoredValue(valueToStore);
      
      // Save to local storage
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      // A more advanced implementation would handle the error case
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
}

/**
 * Hook for managing theme preference in localStorage
 */
export function useTheme() {
  const [theme, setTheme] = useLocalStorage<'light' | 'dark'>('theme', 'light');

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const root = window.document.documentElement;
      
      // Remove previous theme classes
      root.classList.remove('light', 'dark');
      
      // Add current theme class
      root.classList.add(theme);
    }
  }, [theme]);

  return [theme, setTheme] as const;
}

/**
 * Hook for managing time format preference in localStorage
 */
export function useTimeFormat() {
  const [timeFormat, setTimeFormat] = useLocalStorage<'12h' | '24h'>('timeFormat', '12h');
  return [timeFormat, setTimeFormat] as const;
}

/**
 * Interface for timezone selection data
 */
export interface TimeZoneSelection {
  city: string;
  timeZone: string;
  id: string;
}

/**
 * Hook for managing selected timezones in localStorage
 */
export function useTimeZones() {
  const [timeZones, setTimeZones] = useLocalStorage<TimeZoneSelection[]>('selectedTimeZones', []);

  const addTimeZone = (timeZone: TimeZoneSelection) => {
    setTimeZones(prev => {
      // Check if timezone already exists
      const exists = prev.some(tz => tz.timeZone === timeZone.timeZone);
      if (exists) {
        return prev;
      }
      return [...prev, timeZone];
    });
  };

  const removeTimeZone = (id: string) => {
    setTimeZones(prev => prev.filter(tz => tz.id !== id));
  };

  const clearTimeZones = () => {
    setTimeZones([]);
  };

  return {
    timeZones,
    addTimeZone,
    removeTimeZone,
    clearTimeZones,
    setTimeZones
  };
}
