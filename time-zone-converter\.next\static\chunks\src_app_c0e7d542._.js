(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/app/components/Header.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Header)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
'use client';
;
function Header({ timeFormat, onTimeFormatChange, theme, onThemeChange }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
        className: "bg-card border-b border-border p-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-4xl mx-auto flex flex-col sm:flex-row justify-between items-center gap-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center sm:text-left",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-2xl sm:text-3xl font-bold text-foreground",
                            children: "Time Zone Converter"
                        }, void 0, false, {
                            fileName: "[project]/src/app/components/Header.tsx",
                            lineNumber: 22,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-muted-foreground text-sm sm:text-base",
                            children: "Coordinate across time zones effortlessly"
                        }, void 0, false, {
                            fileName: "[project]/src/app/components/Header.tsx",
                            lineNumber: 25,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/components/Header.tsx",
                    lineNumber: 21,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center gap-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-sm text-muted-foreground",
                                    children: "Time:"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/components/Header.tsx",
                                    lineNumber: 33,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>onTimeFormatChange(timeFormat === '12h' ? '24h' : '12h'),
                                    className: "flex items-center bg-secondary rounded-lg p-1 transition-colors",
                                    "aria-label": `Switch to ${timeFormat === '12h' ? '24-hour' : '12-hour'} format`,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: `px-3 py-1 rounded-md text-sm font-medium transition-colors ${timeFormat === '12h' ? 'bg-primary text-primary-foreground' : 'text-secondary-foreground hover:bg-accent'}`,
                                            children: "12h"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/components/Header.tsx",
                                            lineNumber: 39,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: `px-3 py-1 rounded-md text-sm font-medium transition-colors ${timeFormat === '24h' ? 'bg-primary text-primary-foreground' : 'text-secondary-foreground hover:bg-accent'}`,
                                            children: "24h"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/components/Header.tsx",
                                            lineNumber: 48,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/components/Header.tsx",
                                    lineNumber: 34,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/components/Header.tsx",
                            lineNumber: 32,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: ()=>onThemeChange(theme === 'light' ? 'dark' : 'light'),
                            className: "p-2 rounded-lg bg-secondary hover:bg-accent transition-colors",
                            "aria-label": `Switch to ${theme === 'light' ? 'dark' : 'light'} mode`,
                            children: theme === 'light' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                className: "w-5 h-5",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/components/Header.tsx",
                                    lineNumber: 68,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/components/Header.tsx",
                                lineNumber: 67,
                                columnNumber: 15
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                className: "w-5 h-5",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/components/Header.tsx",
                                    lineNumber: 72,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/components/Header.tsx",
                                lineNumber: 71,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/components/Header.tsx",
                            lineNumber: 61,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/components/Header.tsx",
                    lineNumber: 30,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/components/Header.tsx",
            lineNumber: 20,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/components/Header.tsx",
        lineNumber: 19,
        columnNumber: 5
    }, this);
}
_c = Header;
var _c;
__turbopack_context__.k.register(_c, "Header");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/utils/timezones.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Comprehensive list of world cities and their corresponding IANA time zone names
 * This data is used for the city search autocomplete functionality
 */ __turbopack_context__.s({
    "getTimezoneByIdentifier": (()=>getTimezoneByIdentifier),
    "searchTimezones": (()=>searchTimezones),
    "timeZones": (()=>timeZones)
});
const timeZones = [
    // North America
    {
        city: 'New York',
        timeZone: 'America/New_York'
    },
    {
        city: 'Los Angeles',
        timeZone: 'America/Los_Angeles'
    },
    {
        city: 'Chicago',
        timeZone: 'America/Chicago'
    },
    {
        city: 'Denver',
        timeZone: 'America/Denver'
    },
    {
        city: 'Phoenix',
        timeZone: 'America/Phoenix'
    },
    {
        city: 'Las Vegas',
        timeZone: 'America/Los_Angeles'
    },
    {
        city: 'Miami',
        timeZone: 'America/New_York'
    },
    {
        city: 'Seattle',
        timeZone: 'America/Los_Angeles'
    },
    {
        city: 'Boston',
        timeZone: 'America/New_York'
    },
    {
        city: 'San Francisco',
        timeZone: 'America/Los_Angeles'
    },
    {
        city: 'Washington DC',
        timeZone: 'America/New_York'
    },
    {
        city: 'Atlanta',
        timeZone: 'America/New_York'
    },
    {
        city: 'Dallas',
        timeZone: 'America/Chicago'
    },
    {
        city: 'Houston',
        timeZone: 'America/Chicago'
    },
    {
        city: 'Philadelphia',
        timeZone: 'America/New_York'
    },
    {
        city: 'Toronto',
        timeZone: 'America/Toronto'
    },
    {
        city: 'Vancouver',
        timeZone: 'America/Vancouver'
    },
    {
        city: 'Montreal',
        timeZone: 'America/Montreal'
    },
    {
        city: 'Calgary',
        timeZone: 'America/Edmonton'
    },
    {
        city: 'Mexico City',
        timeZone: 'America/Mexico_City'
    },
    {
        city: 'Anchorage',
        timeZone: 'America/Anchorage'
    },
    {
        city: 'Honolulu',
        timeZone: 'Pacific/Honolulu'
    },
    // Europe
    {
        city: 'London',
        timeZone: 'Europe/London'
    },
    {
        city: 'Paris',
        timeZone: 'Europe/Paris'
    },
    {
        city: 'Berlin',
        timeZone: 'Europe/Berlin'
    },
    {
        city: 'Rome',
        timeZone: 'Europe/Rome'
    },
    {
        city: 'Madrid',
        timeZone: 'Europe/Madrid'
    },
    {
        city: 'Amsterdam',
        timeZone: 'Europe/Amsterdam'
    },
    {
        city: 'Brussels',
        timeZone: 'Europe/Brussels'
    },
    {
        city: 'Vienna',
        timeZone: 'Europe/Vienna'
    },
    {
        city: 'Zurich',
        timeZone: 'Europe/Zurich'
    },
    {
        city: 'Stockholm',
        timeZone: 'Europe/Stockholm'
    },
    {
        city: 'Oslo',
        timeZone: 'Europe/Oslo'
    },
    {
        city: 'Copenhagen',
        timeZone: 'Europe/Copenhagen'
    },
    {
        city: 'Helsinki',
        timeZone: 'Europe/Helsinki'
    },
    {
        city: 'Warsaw',
        timeZone: 'Europe/Warsaw'
    },
    {
        city: 'Prague',
        timeZone: 'Europe/Prague'
    },
    {
        city: 'Budapest',
        timeZone: 'Europe/Budapest'
    },
    {
        city: 'Athens',
        timeZone: 'Europe/Athens'
    },
    {
        city: 'Istanbul',
        timeZone: 'Europe/Istanbul'
    },
    {
        city: 'Moscow',
        timeZone: 'Europe/Moscow'
    },
    {
        city: 'Dublin',
        timeZone: 'Europe/Dublin'
    },
    {
        city: 'Lisbon',
        timeZone: 'Europe/Lisbon'
    },
    {
        city: 'Barcelona',
        timeZone: 'Europe/Madrid'
    },
    {
        city: 'Munich',
        timeZone: 'Europe/Berlin'
    },
    {
        city: 'Milan',
        timeZone: 'Europe/Rome'
    },
    // Asia
    {
        city: 'Tokyo',
        timeZone: 'Asia/Tokyo'
    },
    {
        city: 'Beijing',
        timeZone: 'Asia/Shanghai'
    },
    {
        city: 'Shanghai',
        timeZone: 'Asia/Shanghai'
    },
    {
        city: 'Hong Kong',
        timeZone: 'Asia/Hong_Kong'
    },
    {
        city: 'Singapore',
        timeZone: 'Asia/Singapore'
    },
    {
        city: 'Seoul',
        timeZone: 'Asia/Seoul'
    },
    {
        city: 'Mumbai',
        timeZone: 'Asia/Kolkata'
    },
    {
        city: 'Delhi',
        timeZone: 'Asia/Kolkata'
    },
    {
        city: 'Bangalore',
        timeZone: 'Asia/Kolkata'
    },
    {
        city: 'Bangkok',
        timeZone: 'Asia/Bangkok'
    },
    {
        city: 'Jakarta',
        timeZone: 'Asia/Jakarta'
    },
    {
        city: 'Manila',
        timeZone: 'Asia/Manila'
    },
    {
        city: 'Kuala Lumpur',
        timeZone: 'Asia/Kuala_Lumpur'
    },
    {
        city: 'Dubai',
        timeZone: 'Asia/Dubai'
    },
    {
        city: 'Tel Aviv',
        timeZone: 'Asia/Jerusalem'
    },
    {
        city: 'Riyadh',
        timeZone: 'Asia/Riyadh'
    },
    {
        city: 'Doha',
        timeZone: 'Asia/Qatar'
    },
    {
        city: 'Kuwait City',
        timeZone: 'Asia/Kuwait'
    },
    {
        city: 'Taipei',
        timeZone: 'Asia/Taipei'
    },
    {
        city: 'Ho Chi Minh City',
        timeZone: 'Asia/Ho_Chi_Minh'
    },
    {
        city: 'Hanoi',
        timeZone: 'Asia/Ho_Chi_Minh'
    },
    {
        city: 'Yangon',
        timeZone: 'Asia/Yangon'
    },
    {
        city: 'Dhaka',
        timeZone: 'Asia/Dhaka'
    },
    {
        city: 'Karachi',
        timeZone: 'Asia/Karachi'
    },
    {
        city: 'Islamabad',
        timeZone: 'Asia/Karachi'
    },
    // Australia & Oceania
    {
        city: 'Sydney',
        timeZone: 'Australia/Sydney'
    },
    {
        city: 'Melbourne',
        timeZone: 'Australia/Melbourne'
    },
    {
        city: 'Brisbane',
        timeZone: 'Australia/Brisbane'
    },
    {
        city: 'Perth',
        timeZone: 'Australia/Perth'
    },
    {
        city: 'Adelaide',
        timeZone: 'Australia/Adelaide'
    },
    {
        city: 'Auckland',
        timeZone: 'Pacific/Auckland'
    },
    {
        city: 'Wellington',
        timeZone: 'Pacific/Auckland'
    },
    {
        city: 'Fiji',
        timeZone: 'Pacific/Fiji'
    },
    // South America
    {
        city: 'São Paulo',
        timeZone: 'America/Sao_Paulo'
    },
    {
        city: 'Rio de Janeiro',
        timeZone: 'America/Sao_Paulo'
    },
    {
        city: 'Buenos Aires',
        timeZone: 'America/Argentina/Buenos_Aires'
    },
    {
        city: 'Santiago',
        timeZone: 'America/Santiago'
    },
    {
        city: 'Lima',
        timeZone: 'America/Lima'
    },
    {
        city: 'Bogotá',
        timeZone: 'America/Bogota'
    },
    {
        city: 'Caracas',
        timeZone: 'America/Caracas'
    },
    {
        city: 'Montevideo',
        timeZone: 'America/Montevideo'
    },
    // Africa
    {
        city: 'Cairo',
        timeZone: 'Africa/Cairo'
    },
    {
        city: 'Lagos',
        timeZone: 'Africa/Lagos'
    },
    {
        city: 'Johannesburg',
        timeZone: 'Africa/Johannesburg'
    },
    {
        city: 'Cape Town',
        timeZone: 'Africa/Johannesburg'
    },
    {
        city: 'Nairobi',
        timeZone: 'Africa/Nairobi'
    },
    {
        city: 'Casablanca',
        timeZone: 'Africa/Casablanca'
    },
    {
        city: 'Tunis',
        timeZone: 'Africa/Tunis'
    },
    {
        city: 'Algiers',
        timeZone: 'Africa/Algiers'
    },
    {
        city: 'Addis Ababa',
        timeZone: 'Africa/Addis_Ababa'
    },
    {
        city: 'Accra',
        timeZone: 'Africa/Accra'
    },
    // Additional major cities
    {
        city: 'Reykjavik',
        timeZone: 'Atlantic/Reykjavik'
    },
    {
        city: 'Azores',
        timeZone: 'Atlantic/Azores'
    },
    {
        city: 'Canary Islands',
        timeZone: 'Atlantic/Canary'
    },
    {
        city: 'Bermuda',
        timeZone: 'Atlantic/Bermuda'
    },
    {
        city: 'Barbados',
        timeZone: 'America/Barbados'
    },
    {
        city: 'Jamaica',
        timeZone: 'America/Jamaica'
    },
    {
        city: 'Puerto Rico',
        timeZone: 'America/Puerto_Rico'
    },
    {
        city: 'Guam',
        timeZone: 'Pacific/Guam'
    },
    {
        city: 'Samoa',
        timeZone: 'Pacific/Apia'
    },
    {
        city: 'Tahiti',
        timeZone: 'Pacific/Tahiti'
    },
    {
        city: 'Tonga',
        timeZone: 'Pacific/Tongatapu'
    },
    {
        city: 'Vanuatu',
        timeZone: 'Pacific/Efate'
    },
    {
        city: 'New Caledonia',
        timeZone: 'Pacific/Noumea'
    },
    {
        city: 'Solomon Islands',
        timeZone: 'Pacific/Guadalcanal'
    },
    {
        city: 'Marshall Islands',
        timeZone: 'Pacific/Majuro'
    },
    {
        city: 'Palau',
        timeZone: 'Pacific/Palau'
    },
    {
        city: 'Micronesia',
        timeZone: 'Pacific/Chuuk'
    },
    {
        city: 'Kiribati',
        timeZone: 'Pacific/Tarawa'
    },
    {
        city: 'Cook Islands',
        timeZone: 'Pacific/Rarotonga'
    },
    {
        city: 'Easter Island',
        timeZone: 'Pacific/Easter'
    },
    {
        city: 'Galapagos',
        timeZone: 'Pacific/Galapagos'
    },
    {
        city: 'Marquesas',
        timeZone: 'Pacific/Marquesas'
    },
    {
        city: 'Pitcairn',
        timeZone: 'Pacific/Pitcairn'
    },
    {
        city: 'Norfolk Island',
        timeZone: 'Pacific/Norfolk'
    },
    {
        city: 'Lord Howe Island',
        timeZone: 'Australia/Lord_Howe'
    },
    {
        city: 'Chatham Islands',
        timeZone: 'Pacific/Chatham'
    }
];
function getTimezoneByIdentifier(timeZone) {
    return timeZones.find((tz)=>tz.timeZone === timeZone);
}
function searchTimezones(query, limit = 10) {
    const lowercaseQuery = query.toLowerCase();
    return timeZones.filter((tz)=>tz.city.toLowerCase().includes(lowercaseQuery) || tz.timeZone.toLowerCase().includes(lowercaseQuery)).slice(0, limit);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/components/CitySearchInput.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CitySearchInput)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$utils$2f$timezones$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/utils/timezones.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function CitySearchInput({ onCitySelect }) {
    _s();
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [suggestions, setSuggestions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedIndex, setSelectedIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(-1);
    const inputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const listRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Filter suggestions based on search term
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CitySearchInput.useEffect": ()=>{
            if (searchTerm.length < 2) {
                setSuggestions([]);
                setIsOpen(false);
                return;
            }
            const lowercaseSearch = searchTerm.toLowerCase();
            const filtered = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$utils$2f$timezones$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timeZones"].filter({
                "CitySearchInput.useEffect.filtered": (tz)=>{
                    const cityMatch = tz.city.toLowerCase().includes(lowercaseSearch);
                    const timezoneMatch = tz.timeZone.toLowerCase().includes(lowercaseSearch);
                    const regionMatch = tz.timeZone.split('/').pop()?.toLowerCase().includes(lowercaseSearch);
                    return cityMatch || timezoneMatch || regionMatch;
                }
            }["CitySearchInput.useEffect.filtered"]).sort({
                "CitySearchInput.useEffect.filtered": (a, b)=>{
                    // Prioritize exact matches and city name matches
                    const aExactCity = a.city.toLowerCase() === lowercaseSearch;
                    const bExactCity = b.city.toLowerCase() === lowercaseSearch;
                    if (aExactCity && !bExactCity) return -1;
                    if (!aExactCity && bExactCity) return 1;
                    const aCityStart = a.city.toLowerCase().startsWith(lowercaseSearch);
                    const bCityStart = b.city.toLowerCase().startsWith(lowercaseSearch);
                    if (aCityStart && !bCityStart) return -1;
                    if (!aCityStart && bCityStart) return 1;
                    return a.city.localeCompare(b.city);
                }
            }["CitySearchInput.useEffect.filtered"]).slice(0, 8); // Limit to 8 suggestions for performance
            setSuggestions(filtered);
            setIsOpen(filtered.length > 0);
            setSelectedIndex(-1);
        }
    }["CitySearchInput.useEffect"], [
        searchTerm
    ]);
    const handleInputChange = (e)=>{
        setSearchTerm(e.target.value);
    };
    const handleKeyDown = (e)=>{
        if (!isOpen) return;
        switch(e.key){
            case 'ArrowDown':
                e.preventDefault();
                setSelectedIndex((prev)=>prev < suggestions.length - 1 ? prev + 1 : prev);
                break;
            case 'ArrowUp':
                e.preventDefault();
                setSelectedIndex((prev)=>prev > 0 ? prev - 1 : -1);
                break;
            case 'Enter':
                e.preventDefault();
                if (selectedIndex >= 0 && suggestions[selectedIndex]) {
                    handleCitySelect(suggestions[selectedIndex]);
                }
                break;
            case 'Escape':
                setIsOpen(false);
                setSelectedIndex(-1);
                inputRef.current?.blur();
                break;
        }
    };
    const handleCitySelect = (city)=>{
        const newCity = {
            ...city,
            id: `${city.timeZone}-${Date.now()}`
        };
        onCitySelect(newCity);
        setSearchTerm('');
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.focus();
    };
    const handleClickOutside = (e)=>{
        if (inputRef.current && !inputRef.current.contains(e.target) && listRef.current && !listRef.current.contains(e.target)) {
            setIsOpen(false);
            setSelectedIndex(-1);
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CitySearchInput.useEffect": ()=>{
            document.addEventListener('mousedown', handleClickOutside);
            return ({
                "CitySearchInput.useEffect": ()=>document.removeEventListener('mousedown', handleClickOutside)
            })["CitySearchInput.useEffect"];
        }
    }["CitySearchInput.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative w-full max-w-md mx-auto",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        ref: inputRef,
                        type: "text",
                        value: searchTerm,
                        onChange: handleInputChange,
                        onKeyDown: handleKeyDown,
                        placeholder: "Search for a city or timezone...",
                        className: "w-full px-4 py-3 pl-10 bg-card border border-border rounded-lg text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-colors",
                        "aria-label": "Search for city or timezone",
                        "aria-expanded": isOpen,
                        "aria-haspopup": "listbox",
                        role: "combobox"
                    }, void 0, false, {
                        fileName: "[project]/src/app/components/CitySearchInput.tsx",
                        lineNumber: 119,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                        className: "absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground",
                        fill: "none",
                        stroke: "currentColor",
                        viewBox: "0 0 24 24",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            strokeWidth: 2,
                            d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        }, void 0, false, {
                            fileName: "[project]/src/app/components/CitySearchInput.tsx",
                            lineNumber: 141,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/components/CitySearchInput.tsx",
                        lineNumber: 135,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/components/CitySearchInput.tsx",
                lineNumber: 118,
                columnNumber: 7
            }, this),
            isOpen && suggestions.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                ref: listRef,
                className: "absolute z-50 w-full mt-1 bg-card border border-border rounded-lg shadow-lg max-h-64 overflow-y-auto",
                role: "listbox",
                children: suggestions.map((suggestion, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                        className: `px-4 py-3 cursor-pointer transition-colors border-b border-border last:border-b-0 ${index === selectedIndex ? 'bg-accent text-accent-foreground' : 'hover:bg-accent hover:text-accent-foreground'}`,
                        onClick: ()=>handleCitySelect(suggestion),
                        role: "option",
                        "aria-selected": index === selectedIndex,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-between items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "font-medium",
                                    children: suggestion.city
                                }, void 0, false, {
                                    fileName: "[project]/src/app/components/CitySearchInput.tsx",
                                    lineNumber: 164,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-sm text-muted-foreground",
                                    children: suggestion.timeZone.split('/').pop()?.replace('_', ' ')
                                }, void 0, false, {
                                    fileName: "[project]/src/app/components/CitySearchInput.tsx",
                                    lineNumber: 165,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/components/CitySearchInput.tsx",
                            lineNumber: 163,
                            columnNumber: 15
                        }, this)
                    }, `${suggestion.timeZone}-${index}`, false, {
                        fileName: "[project]/src/app/components/CitySearchInput.tsx",
                        lineNumber: 152,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/app/components/CitySearchInput.tsx",
                lineNumber: 146,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/components/CitySearchInput.tsx",
        lineNumber: 117,
        columnNumber: 5
    }, this);
}
_s(CitySearchInput, "X48gee9YIH2sWlB3ePlnZsKVcW4=");
_c = CitySearchInput;
var _c;
__turbopack_context__.k.register(_c, "CitySearchInput");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/components/TimeZoneCard.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>TimeZoneCard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2d$tz$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns-tz/dist/esm/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2d$tz$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns-tz/dist/esm/index.js [app-client] (ecmascript) <exports>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function TimeZoneCard({ city, timeZone, id, timeFormat, referenceTime, onTimeChange, onRemove, showTimeDifference = false, userTimeZone }) {
    _s();
    const [currentTime, setCurrentTime] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(new Date());
    const [isEditing, setIsEditing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [editTime, setEditTime] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const inputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Update current time every second or when reference time changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TimeZoneCard.useEffect": ()=>{
            const updateTime = {
                "TimeZoneCard.useEffect.updateTime": ()=>{
                    if (referenceTime) {
                        // Use reference time to calculate this timezone's time
                        const zonedTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2d$tz$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["utcToZonedTime"])(referenceTime, timeZone);
                        setCurrentTime(zonedTime);
                    } else {
                        // Use current time
                        const now = new Date();
                        const zonedTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2d$tz$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["utcToZonedTime"])(now, timeZone);
                        setCurrentTime(zonedTime);
                    }
                }
            }["TimeZoneCard.useEffect.updateTime"];
            updateTime();
            const interval = setInterval(updateTime, 1000);
            return ({
                "TimeZoneCard.useEffect": ()=>clearInterval(interval)
            })["TimeZoneCard.useEffect"];
        }
    }["TimeZoneCard.useEffect"], [
        timeZone,
        referenceTime
    ]);
    // Format time based on selected format
    const formatTime = (date)=>{
        try {
            if (timeFormat === '12h') {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, 'h:mm:ss a');
            } else {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, 'HH:mm:ss');
            }
        } catch (error) {
            console.error('Error formatting time:', error);
            return '00:00:00';
        }
    };
    // Format date
    const formatDate = (date)=>{
        try {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, 'EEEE, MMMM d, yyyy');
        } catch (error) {
            console.error('Error formatting date:', error);
            return '';
        }
    };
    // Get timezone abbreviation
    const getTimezoneAbbr = ()=>{
        try {
            return new Intl.DateTimeFormat('en', {
                timeZone,
                timeZoneName: 'short'
            }).formatToParts(currentTime).find((part)=>part.type === 'timeZoneName')?.value || timeZone.split('/').pop();
        } catch (error) {
            return timeZone.split('/').pop();
        }
    };
    // Calculate time difference from user's timezone
    const getTimeDifference = ()=>{
        if (!showTimeDifference || !userTimeZone) return null;
        try {
            const now = new Date();
            const userTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2d$tz$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["utcToZonedTime"])(now, userTimeZone);
            const cardTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2d$tz$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["utcToZonedTime"])(now, timeZone);
            const diffMs = cardTime.getTime() - userTime.getTime();
            const diffHours = Math.round(diffMs / (1000 * 60 * 60));
            if (diffHours === 0) return 'Same time';
            const sign = diffHours > 0 ? '+' : '';
            const absHours = Math.abs(diffHours);
            if (absHours === 1) {
                return `${sign}${diffHours} hour`;
            } else {
                return `${sign}${diffHours} hours`;
            }
        } catch (error) {
            return null;
        }
    };
    const handleTimeClick = ()=>{
        setIsEditing(true);
        setEditTime((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(currentTime, timeFormat === '12h' ? 'h:mm a' : 'HH:mm'));
    };
    const handleTimeSubmit = ()=>{
        try {
            let newTime;
            if (timeFormat === '12h') {
                // Parse 12-hour format (e.g., "2:30 PM")
                const trimmedTime = editTime.trim();
                const match = trimmedTime.match(/^(\d{1,2}):(\d{2})\s*(AM|PM|am|pm)$/i);
                if (!match) {
                    console.error('Invalid 12-hour time format');
                    setIsEditing(false);
                    return;
                }
                let hours = parseInt(match[1], 10);
                const minutes = parseInt(match[2], 10);
                const period = match[3].toUpperCase();
                if (hours < 1 || hours > 12 || minutes < 0 || minutes > 59) {
                    console.error('Invalid time values');
                    setIsEditing(false);
                    return;
                }
                if (period === 'PM' && hours !== 12) {
                    hours += 12;
                } else if (period === 'AM' && hours === 12) {
                    hours = 0;
                }
                newTime = new Date(currentTime);
                newTime.setHours(hours, minutes, 0, 0);
            } else {
                // Parse 24-hour format (e.g., "14:30")
                const match = editTime.trim().match(/^(\d{1,2}):(\d{2})$/);
                if (!match) {
                    console.error('Invalid 24-hour time format');
                    setIsEditing(false);
                    return;
                }
                const hours = parseInt(match[1], 10);
                const minutes = parseInt(match[2], 10);
                if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
                    console.error('Invalid time values');
                    setIsEditing(false);
                    return;
                }
                newTime = new Date(currentTime);
                newTime.setHours(hours, minutes, 0, 0);
            }
            // Convert to UTC for reference
            const utcTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2d$tz$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["zonedTimeToUtc"])(newTime, timeZone);
            onTimeChange(utcTime);
            setIsEditing(false);
        } catch (error) {
            console.error('Error parsing time:', error);
            setIsEditing(false);
        }
    };
    const handleKeyDown = (e)=>{
        if (e.key === 'Enter') {
            handleTimeSubmit();
        } else if (e.key === 'Escape') {
            setIsEditing(false);
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TimeZoneCard.useEffect": ()=>{
            if (isEditing && inputRef.current) {
                inputRef.current.focus();
                inputRef.current.select();
            }
        }
    }["TimeZoneCard.useEffect"], [
        isEditing
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-card border border-border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-between items-start mb-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-semibold text-foreground",
                                children: city
                            }, void 0, false, {
                                fileName: "[project]/src/app/components/TimeZoneCard.tsx",
                                lineNumber: 210,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm text-muted-foreground",
                                        children: getTimezoneAbbr()
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/components/TimeZoneCard.tsx",
                                        lineNumber: 212,
                                        columnNumber: 13
                                    }, this),
                                    showTimeDifference && getTimeDifference() && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-xs bg-secondary text-secondary-foreground px-2 py-1 rounded-full",
                                        children: getTimeDifference()
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/components/TimeZoneCard.tsx",
                                        lineNumber: 214,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/components/TimeZoneCard.tsx",
                                lineNumber: 211,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/components/TimeZoneCard.tsx",
                        lineNumber: 209,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>onRemove(id),
                        className: "p-1 text-muted-foreground hover:text-destructive transition-colors",
                        "aria-label": `Remove ${city}`,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            className: "w-5 h-5",
                            fill: "none",
                            stroke: "currentColor",
                            viewBox: "0 0 24 24",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M6 18L18 6M6 6l12 12"
                            }, void 0, false, {
                                fileName: "[project]/src/app/components/TimeZoneCard.tsx",
                                lineNumber: 226,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/components/TimeZoneCard.tsx",
                            lineNumber: 225,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/components/TimeZoneCard.tsx",
                        lineNumber: 220,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/components/TimeZoneCard.tsx",
                lineNumber: 208,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-muted-foreground",
                        children: formatDate(currentTime)
                    }, void 0, false, {
                        fileName: "[project]/src/app/components/TimeZoneCard.tsx",
                        lineNumber: 232,
                        columnNumber: 9
                    }, this),
                    isEditing ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        ref: inputRef,
                        type: "text",
                        value: editTime,
                        onChange: (e)=>setEditTime(e.target.value),
                        onBlur: handleTimeSubmit,
                        onKeyDown: handleKeyDown,
                        className: "text-2xl font-mono font-bold bg-input border border-border rounded px-2 py-1 w-full focus:outline-none focus:ring-2 focus:ring-ring",
                        placeholder: timeFormat === '12h' ? '12:00 PM' : '12:00'
                    }, void 0, false, {
                        fileName: "[project]/src/app/components/TimeZoneCard.tsx",
                        lineNumber: 235,
                        columnNumber: 11
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleTimeClick,
                        className: "text-2xl font-mono font-bold text-foreground hover:text-primary transition-colors cursor-pointer",
                        "aria-label": `Edit time for ${city}. Current time: ${formatTime(currentTime)}`,
                        children: formatTime(currentTime)
                    }, void 0, false, {
                        fileName: "[project]/src/app/components/TimeZoneCard.tsx",
                        lineNumber: 246,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/components/TimeZoneCard.tsx",
                lineNumber: 231,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/components/TimeZoneCard.tsx",
        lineNumber: 207,
        columnNumber: 5
    }, this);
}
_s(TimeZoneCard, "p1WMpp6l7Hoyb1qMuAPTUpvaYhI=");
_c = TimeZoneCard;
var _c;
__turbopack_context__.k.register(_c, "TimeZoneCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/components/TimeZoneList.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>TimeZoneList)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$components$2f$TimeZoneCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/components/TimeZoneCard.tsx [app-client] (ecmascript)");
'use client';
;
;
function TimeZoneList({ timeZones, timeFormat, referenceTime, onTimeChange, onRemoveTimeZone, showTimeDifference = false, userTimeZone }) {
    if (timeZones.length === 0) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "text-center py-12",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-md mx-auto",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                        className: "w-16 h-16 mx-auto text-muted-foreground mb-4",
                        fill: "none",
                        stroke: "currentColor",
                        viewBox: "0 0 24 24",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            strokeWidth: 1.5,
                            d: "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                        }, void 0, false, {
                            fileName: "[project]/src/app/components/TimeZoneList.tsx",
                            lineNumber: 41,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/components/TimeZoneList.tsx",
                        lineNumber: 35,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-medium text-foreground mb-2",
                        children: "No time zones added yet"
                    }, void 0, false, {
                        fileName: "[project]/src/app/components/TimeZoneList.tsx",
                        lineNumber: 48,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-muted-foreground",
                        children: "Search for a city above to add your first time zone and start converting times."
                    }, void 0, false, {
                        fileName: "[project]/src/app/components/TimeZoneList.tsx",
                        lineNumber: 51,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/components/TimeZoneList.tsx",
                lineNumber: 34,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/components/TimeZoneList.tsx",
            lineNumber: 33,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "grid gap-4 md:grid-cols-2 lg:grid-cols-3",
        children: timeZones.map((tz)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$components$2f$TimeZoneCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                city: tz.city,
                timeZone: tz.timeZone,
                id: tz.id,
                timeFormat: timeFormat,
                referenceTime: referenceTime,
                onTimeChange: onTimeChange,
                onRemove: onRemoveTimeZone,
                showTimeDifference: showTimeDifference,
                userTimeZone: userTimeZone
            }, tz.id, false, {
                fileName: "[project]/src/app/components/TimeZoneList.tsx",
                lineNumber: 62,
                columnNumber: 9
            }, this))
    }, void 0, false, {
        fileName: "[project]/src/app/components/TimeZoneList.tsx",
        lineNumber: 60,
        columnNumber: 5
    }, this);
}
_c = TimeZoneList;
var _c;
__turbopack_context__.k.register(_c, "TimeZoneList");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/components/QuickAddTimezones.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>QuickAddTimezones)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
'use client';
;
const popularTimezones = [
    {
        city: 'New York',
        timeZone: 'America/New_York'
    },
    {
        city: 'Los Angeles',
        timeZone: 'America/Los_Angeles'
    },
    {
        city: 'London',
        timeZone: 'Europe/London'
    },
    {
        city: 'Paris',
        timeZone: 'Europe/Paris'
    },
    {
        city: 'Tokyo',
        timeZone: 'Asia/Tokyo'
    },
    {
        city: 'Sydney',
        timeZone: 'Australia/Sydney'
    },
    {
        city: 'Dubai',
        timeZone: 'Asia/Dubai'
    },
    {
        city: 'Singapore',
        timeZone: 'Asia/Singapore'
    },
    {
        city: 'Hong Kong',
        timeZone: 'Asia/Hong_Kong'
    },
    {
        city: 'Mumbai',
        timeZone: 'Asia/Kolkata'
    },
    {
        city: 'São Paulo',
        timeZone: 'America/Sao_Paulo'
    },
    {
        city: 'Mexico City',
        timeZone: 'America/Mexico_City'
    }
];
function QuickAddTimezones({ onCitySelect, existingTimezones }) {
    const availableTimezones = popularTimezones.filter((tz)=>!existingTimezones.includes(tz.timeZone));
    if (availableTimezones.length === 0) {
        return null;
    }
    const handleQuickAdd = (timezone)=>{
        onCitySelect({
            ...timezone,
            id: `quick-${timezone.timeZone}-${Date.now()}`
        });
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-card border border-border rounded-lg p-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                className: "text-sm font-medium text-foreground mb-3",
                children: "Quick Add Popular Cities"
            }, void 0, false, {
                fileName: "[project]/src/app/components/QuickAddTimezones.tsx",
                lineNumber: 43,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-wrap gap-2",
                children: availableTimezones.slice(0, 8).map((timezone)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>handleQuickAdd(timezone),
                        className: "px-3 py-1.5 text-sm bg-secondary text-secondary-foreground rounded-md hover:bg-accent hover:text-accent-foreground transition-colors",
                        children: timezone.city
                    }, timezone.timeZone, false, {
                        fileName: "[project]/src/app/components/QuickAddTimezones.tsx",
                        lineNumber: 46,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/app/components/QuickAddTimezones.tsx",
                lineNumber: 44,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/components/QuickAddTimezones.tsx",
        lineNumber: 42,
        columnNumber: 5
    }, this);
}
_c = QuickAddTimezones;
var _c;
__turbopack_context__.k.register(_c, "QuickAddTimezones");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/hooks/useLocalStorage.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useLocalStorage": (()=>useLocalStorage),
    "useTheme": (()=>useTheme),
    "useTimeFormat": (()=>useTimeFormat),
    "useTimeZones": (()=>useTimeZones)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
'use client';
;
function useLocalStorage(key, initialValue) {
    _s();
    // State to store our value
    // Pass initial state function to useState so logic is only executed once
    const [storedValue, setStoredValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "useLocalStorage.useState": ()=>{
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            try {
                // Get from local storage by key
                const item = window.localStorage.getItem(key);
                // Parse stored json or if none return initialValue
                return item ? JSON.parse(item) : initialValue;
            } catch (error) {
                // If error also return initialValue
                console.error(`Error reading localStorage key "${key}":`, error);
                return initialValue;
            }
        }
    }["useLocalStorage.useState"]);
    // Return a wrapped version of useState's setter function that ...
    // ... persists the new value to localStorage.
    const setValue = (value)=>{
        try {
            // Allow value to be a function so we have the same API as useState
            const valueToStore = value instanceof Function ? value(storedValue) : value;
            // Save state
            setStoredValue(valueToStore);
            // Save to local storage
            if ("TURBOPACK compile-time truthy", 1) {
                window.localStorage.setItem(key, JSON.stringify(valueToStore));
            }
        } catch (error) {
            // A more advanced implementation would handle the error case
            console.error(`Error setting localStorage key "${key}":`, error);
        }
    };
    return [
        storedValue,
        setValue
    ];
}
_s(useLocalStorage, "PBqOeCWnlu5GQ+g4d8OyLO29GPM=");
function useTheme() {
    _s1();
    const [theme, setTheme] = useLocalStorage('theme', 'light');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useTheme.useEffect": ()=>{
            if ("TURBOPACK compile-time truthy", 1) {
                const root = window.document.documentElement;
                // Remove previous theme classes
                root.classList.remove('light', 'dark');
                // Add current theme class
                root.classList.add(theme);
            }
        }
    }["useTheme.useEffect"], [
        theme
    ]);
    return [
        theme,
        setTheme
    ];
}
_s1(useTheme, "YGS+s8CVEWRviNom18nwlIGWkHI=", false, function() {
    return [
        useLocalStorage
    ];
});
function useTimeFormat() {
    _s2();
    const [timeFormat, setTimeFormat] = useLocalStorage('timeFormat', '12h');
    return [
        timeFormat,
        setTimeFormat
    ];
}
_s2(useTimeFormat, "ji+VxJlFNTUArdfhx6fqkah4YU4=", false, function() {
    return [
        useLocalStorage
    ];
});
function useTimeZones() {
    _s3();
    const [timeZones, setTimeZones] = useLocalStorage('selectedTimeZones', []);
    const addTimeZone = (timeZone)=>{
        setTimeZones((prev)=>{
            // Check if timezone already exists
            const exists = prev.some((tz)=>tz.timeZone === timeZone.timeZone);
            if (exists) {
                return prev;
            }
            return [
                ...prev,
                timeZone
            ];
        });
    };
    const removeTimeZone = (id)=>{
        setTimeZones((prev)=>prev.filter((tz)=>tz.id !== id));
    };
    const clearTimeZones = ()=>{
        setTimeZones([]);
    };
    return {
        timeZones,
        addTimeZone,
        removeTimeZone,
        clearTimeZones,
        setTimeZones
    };
}
_s3(useTimeZones, "PnPRRtiK+zT9B9jmxRYBV/SvH48=", false, function() {
    return [
        useLocalStorage
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/utils/time.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "convertTimeZone": (()=>convertTimeZone),
    "createTimeInTimezone": (()=>createTimeInTimezone),
    "formatDate": (()=>formatDate),
    "formatTime": (()=>formatTime),
    "getTimezoneAbbreviation": (()=>getTimezoneAbbreviation),
    "getUserTimezone": (()=>getUserTimezone),
    "isValidTimezone": (()=>isValidTimezone),
    "parseTimeString": (()=>parseTimeString)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2d$tz$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns-tz/dist/esm/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2d$tz$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns-tz/dist/esm/index.js [app-client] (ecmascript) <exports>");
;
;
function getUserTimezone() {
    try {
        return Intl.DateTimeFormat().resolvedOptions().timeZone;
    } catch (error) {
        console.error('Error getting user timezone:', error);
        return 'UTC';
    }
}
function convertTimeZone(date, fromTimeZone, toTimeZone) {
    try {
        // Convert to UTC first, then to target timezone
        const utcTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2d$tz$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["zonedTimeToUtc"])(date, fromTimeZone);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2d$tz$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["utcToZonedTime"])(utcTime, toTimeZone);
    } catch (error) {
        console.error('Error converting timezone:', error);
        return date;
    }
}
function formatTime(date, format12h = false) {
    try {
        if (format12h) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, 'h:mm:ss a');
        } else {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, 'HH:mm:ss');
        }
    } catch (error) {
        console.error('Error formatting time:', error);
        return '00:00:00';
    }
}
function formatDate(date) {
    try {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, 'EEEE, MMMM d, yyyy');
    } catch (error) {
        console.error('Error formatting date:', error);
        return '';
    }
}
function getTimezoneAbbreviation(timeZone, date = new Date()) {
    try {
        const formatter = new Intl.DateTimeFormat('en', {
            timeZone,
            timeZoneName: 'short'
        });
        const parts = formatter.formatToParts(date);
        const timeZonePart = parts.find((part)=>part.type === 'timeZoneName');
        return timeZonePart?.value || timeZone.split('/').pop()?.replace('_', ' ') || timeZone;
    } catch (error) {
        console.error('Error getting timezone abbreviation:', error);
        return timeZone.split('/').pop()?.replace('_', ' ') || timeZone;
    }
}
function parseTimeString(timeString, is12Hour = false) {
    try {
        if (is12Hour) {
            // Handle 12-hour format (e.g., "2:30 PM", "2:30PM", "2:30 pm")
            const match = timeString.match(/^(\d{1,2}):(\d{2})\s*(AM|PM|am|pm)$/i);
            if (!match) return null;
            let hours = parseInt(match[1], 10);
            const minutes = parseInt(match[2], 10);
            const period = match[3].toUpperCase();
            if (period === 'PM' && hours !== 12) {
                hours += 12;
            } else if (period === 'AM' && hours === 12) {
                hours = 0;
            }
            return {
                hours,
                minutes
            };
        } else {
            // Handle 24-hour format (e.g., "14:30")
            const match = timeString.match(/^(\d{1,2}):(\d{2})$/);
            if (!match) return null;
            const hours = parseInt(match[1], 10);
            const minutes = parseInt(match[2], 10);
            if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
                return null;
            }
            return {
                hours,
                minutes
            };
        }
    } catch (error) {
        console.error('Error parsing time string:', error);
        return null;
    }
}
function createTimeInTimezone(baseDate, hours, minutes, timeZone) {
    try {
        const newDate = new Date(baseDate);
        newDate.setHours(hours, minutes, 0, 0);
        // Convert to UTC for consistent handling
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2d$tz$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["zonedTimeToUtc"])(newDate, timeZone);
    } catch (error) {
        console.error('Error creating time in timezone:', error);
        return baseDate;
    }
}
function isValidTimezone(timeZone) {
    try {
        Intl.DateTimeFormat(undefined, {
            timeZone
        });
        return true;
    } catch (error) {
        return false;
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Home)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$components$2f$Header$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/components/Header.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$components$2f$CitySearchInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/components/CitySearchInput.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$components$2f$TimeZoneList$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/components/TimeZoneList.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$components$2f$QuickAddTimezones$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/components/QuickAddTimezones.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$hooks$2f$useLocalStorage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/hooks/useLocalStorage.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$utils$2f$time$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/utils/time.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$utils$2f$timezones$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/utils/timezones.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
function Home() {
    _s();
    const [theme, setTheme] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$hooks$2f$useLocalStorage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"])();
    const [timeFormat, setTimeFormat] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$hooks$2f$useLocalStorage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTimeFormat"])();
    const { timeZones, addTimeZone, removeTimeZone } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$hooks$2f$useLocalStorage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTimeZones"])();
    const [referenceTime, setReferenceTime] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [showTimeDifference, setShowTimeDifference] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [userTimeZone] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "Home.useState": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$utils$2f$time$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getUserTimezone"])()
    }["Home.useState"]);
    // Initialize with user's local timezone on first load
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Home.useEffect": ()=>{
            if (timeZones.length === 0) {
                const userTimezone = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$utils$2f$time$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getUserTimezone"])();
                const localCity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$utils$2f$timezones$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTimezoneByIdentifier"])(userTimezone);
                if (localCity) {
                    addTimeZone({
                        ...localCity,
                        id: `local-${Date.now()}`
                    });
                } else {
                    // Fallback: add a generic local timezone entry
                    addTimeZone({
                        city: 'Local Time',
                        timeZone: userTimezone,
                        id: `local-${Date.now()}`
                    });
                }
            }
        }
    }["Home.useEffect"], [
        timeZones.length,
        addTimeZone
    ]);
    const handleCitySelect = (city)=>{
        addTimeZone(city);
    };
    const handleTimeChange = (newTime)=>{
        setReferenceTime(newTime);
    };
    const handleRemoveTimeZone = (id)=>{
        removeTimeZone(id);
        // Reset reference time when removing a timezone
        if (referenceTime) {
            setReferenceTime(null);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-background",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$components$2f$Header$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                timeFormat: timeFormat,
                onTimeFormatChange: setTimeFormat,
                theme: theme,
                onThemeChange: setTheme
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 60,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                className: "max-w-6xl mx-auto px-4 py-8",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$components$2f$CitySearchInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                onCitySelect: handleCitySelect
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 71,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 70,
                            columnNumber: 11
                        }, this),
                        timeZones.length < 6 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "max-w-2xl mx-auto",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$components$2f$QuickAddTimezones$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                onCitySelect: handleCitySelect,
                                existingTimezones: timeZones.map((tz)=>tz.timeZone)
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 77,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 76,
                            columnNumber: 13
                        }, this),
                        timeZones.length > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>setShowTimeDifference(!showTimeDifference),
                                className: "flex items-center gap-2 px-4 py-2 bg-secondary text-secondary-foreground rounded-lg hover:bg-accent transition-colors",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        className: "w-4 h-4",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 2,
                                            d: "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 92,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 91,
                                        columnNumber: 17
                                    }, this),
                                    showTimeDifference ? 'Hide' : 'Show',
                                    " time differences"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 87,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 86,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$components$2f$TimeZoneList$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            timeZones: timeZones,
                            timeFormat: timeFormat,
                            referenceTime: referenceTime,
                            onTimeChange: handleTimeChange,
                            onRemoveTimeZone: handleRemoveTimeZone,
                            showTimeDifference: showTimeDifference,
                            userTimeZone: userTimeZone
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 100,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 68,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 67,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("footer", {
                className: "mt-16 py-8 border-t border-border",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-6xl mx-auto px-4 text-center",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-muted-foreground text-sm",
                        children: [
                            "Built with ❤️ by",
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                href: "https://github.com/chirag127",
                                target: "_blank",
                                rel: "noopener noreferrer",
                                className: "text-primary hover:underline",
                                children: "Chirag Singhal"
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 116,
                                columnNumber: 13
                            }, this),
                            ' ',
                            "• Open source on",
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                href: "https://github.com/chirag127/Time-Zone-Converter",
                                target: "_blank",
                                rel: "noopener noreferrer",
                                className: "text-primary hover:underline",
                                children: "GitHub"
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 125,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 114,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 113,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 112,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 59,
        columnNumber: 5
    }, this);
}
_s(Home, "GUWrb+I5aDvLnd2Q9nfCrBUfFVw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$hooks$2f$useLocalStorage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$hooks$2f$useLocalStorage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTimeFormat"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$hooks$2f$useLocalStorage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTimeZones"]
    ];
});
_c = Home;
var _c;
__turbopack_context__.k.register(_c, "Home");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_app_c0e7d542._.js.map