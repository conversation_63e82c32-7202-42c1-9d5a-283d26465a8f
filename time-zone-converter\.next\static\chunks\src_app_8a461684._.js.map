{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/src/app/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface HeaderProps {\n  timeFormat: '12h' | '24h';\n  onTimeFormatChange: (format: '12h' | '24h') => void;\n  theme: 'light' | 'dark';\n  onThemeChange: (theme: 'light' | 'dark') => void;\n}\n\nexport default function Header({ \n  timeFormat, \n  onTimeFormatChange, \n  theme, \n  onThemeChange \n}: HeaderProps) {\n  return (\n    <header className=\"bg-card border-b border-border p-4\">\n      <div className=\"max-w-4xl mx-auto flex flex-col sm:flex-row justify-between items-center gap-4\">\n        <div className=\"text-center sm:text-left\">\n          <h1 className=\"text-2xl sm:text-3xl font-bold text-foreground\">\n            Time Zone Converter\n          </h1>\n          <p className=\"text-muted-foreground text-sm sm:text-base\">\n            Coordinate across time zones effortlessly\n          </p>\n        </div>\n        \n        <div className=\"flex items-center gap-4\">\n          {/* Time Format Toggle */}\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-sm text-muted-foreground\">Time:</span>\n            <button\n              onClick={() => onTimeFormatChange(timeFormat === '12h' ? '24h' : '12h')}\n              className=\"flex items-center bg-secondary rounded-lg p-1 transition-colors\"\n              aria-label={`Switch to ${timeFormat === '12h' ? '24-hour' : '12-hour'} format`}\n            >\n              <span\n                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n                  timeFormat === '12h'\n                    ? 'bg-primary text-primary-foreground'\n                    : 'text-secondary-foreground hover:bg-accent'\n                }`}\n              >\n                12h\n              </span>\n              <span\n                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n                  timeFormat === '24h'\n                    ? 'bg-primary text-primary-foreground'\n                    : 'text-secondary-foreground hover:bg-accent'\n                }`}\n              >\n                24h\n              </span>\n            </button>\n          </div>\n\n          {/* Theme Toggle */}\n          <button\n            onClick={() => onThemeChange(theme === 'light' ? 'dark' : 'light')}\n            className=\"p-2 rounded-lg bg-secondary hover:bg-accent transition-colors\"\n            aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}\n          >\n            {theme === 'light' ? (\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\" />\n              </svg>\n            ) : (\n              <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\" />\n              </svg>\n            )}\n          </button>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAWe,SAAS,OAAO,EAC7B,UAAU,EACV,kBAAkB,EAClB,KAAK,EACL,aAAa,EACD;IACZ,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,6LAAC;4BAAE,WAAU;sCAA6C;;;;;;;;;;;;8BAK5D,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAgC;;;;;;8CAChD,6LAAC;oCACC,SAAS,IAAM,mBAAmB,eAAe,QAAQ,QAAQ;oCACjE,WAAU;oCACV,cAAY,CAAC,UAAU,EAAE,eAAe,QAAQ,YAAY,UAAU,OAAO,CAAC;;sDAE9E,6LAAC;4CACC,WAAW,CAAC,2DAA2D,EACrE,eAAe,QACX,uCACA,6CACJ;sDACH;;;;;;sDAGD,6LAAC;4CACC,WAAW,CAAC,2DAA2D,EACrE,eAAe,QACX,uCACA,6CACJ;sDACH;;;;;;;;;;;;;;;;;;sCAOL,6LAAC;4BACC,SAAS,IAAM,cAAc,UAAU,UAAU,SAAS;4BAC1D,WAAU;4BACV,cAAY,CAAC,UAAU,EAAE,UAAU,UAAU,SAAS,QAAQ,KAAK,CAAC;sCAEnE,UAAU,wBACT,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;qDAGvE,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF;KApEwB", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/src/app/utils/timezones.ts"], "sourcesContent": ["/**\n * Comprehensive list of world cities and their corresponding IANA time zone names\n * This data is used for the city search autocomplete functionality\n */\nexport const timeZones = [\n  // North America\n  { city: 'New York', timeZone: 'America/New_York' },\n  { city: 'Los Angeles', timeZone: 'America/Los_Angeles' },\n  { city: 'Chicago', timeZone: 'America/Chicago' },\n  { city: 'Denver', timeZone: 'America/Denver' },\n  { city: 'Phoenix', timeZone: 'America/Phoenix' },\n  { city: 'Las Vegas', timeZone: 'America/Los_Angeles' },\n  { city: 'Miami', timeZone: 'America/New_York' },\n  { city: 'Seattle', timeZone: 'America/Los_Angeles' },\n  { city: 'Boston', timeZone: 'America/New_York' },\n  { city: 'San Francisco', timeZone: 'America/Los_Angeles' },\n  { city: 'Washington DC', timeZone: 'America/New_York' },\n  { city: 'Atlanta', timeZone: 'America/New_York' },\n  { city: 'Dallas', timeZone: 'America/Chicago' },\n  { city: 'Houston', timeZone: 'America/Chicago' },\n  { city: 'Philadelphia', timeZone: 'America/New_York' },\n  { city: 'Toronto', timeZone: 'America/Toronto' },\n  { city: 'Vancouver', timeZone: 'America/Vancouver' },\n  { city: 'Montreal', timeZone: 'America/Montreal' },\n  { city: 'Calgary', timeZone: 'America/Edmonton' },\n  { city: 'Mexico City', timeZone: 'America/Mexico_City' },\n  { city: 'Anchorage', timeZone: 'America/Anchorage' },\n  { city: 'Honolulu', timeZone: 'Pacific/Honolulu' },\n\n  // Europe\n  { city: 'London', timeZone: 'Europe/London' },\n  { city: 'Paris', timeZone: 'Europe/Paris' },\n  { city: 'Berlin', timeZone: 'Europe/Berlin' },\n  { city: 'Rome', timeZone: 'Europe/Rome' },\n  { city: 'Madrid', timeZone: 'Europe/Madrid' },\n  { city: 'Amsterdam', timeZone: 'Europe/Amsterdam' },\n  { city: 'Brussels', timeZone: 'Europe/Brussels' },\n  { city: 'Vienna', timeZone: 'Europe/Vienna' },\n  { city: 'Zurich', timeZone: 'Europe/Zurich' },\n  { city: 'Stockholm', timeZone: 'Europe/Stockholm' },\n  { city: 'Oslo', timeZone: 'Europe/Oslo' },\n  { city: 'Copenhagen', timeZone: 'Europe/Copenhagen' },\n  { city: 'Helsinki', timeZone: 'Europe/Helsinki' },\n  { city: 'Warsaw', timeZone: 'Europe/Warsaw' },\n  { city: 'Prague', timeZone: 'Europe/Prague' },\n  { city: 'Budapest', timeZone: 'Europe/Budapest' },\n  { city: 'Athens', timeZone: 'Europe/Athens' },\n  { city: 'Istanbul', timeZone: 'Europe/Istanbul' },\n  { city: 'Moscow', timeZone: 'Europe/Moscow' },\n  { city: 'Dublin', timeZone: 'Europe/Dublin' },\n  { city: 'Lisbon', timeZone: 'Europe/Lisbon' },\n  { city: 'Barcelona', timeZone: 'Europe/Madrid' },\n  { city: 'Munich', timeZone: 'Europe/Berlin' },\n  { city: 'Milan', timeZone: 'Europe/Rome' },\n\n  // Asia\n  { city: 'Tokyo', timeZone: 'Asia/Tokyo' },\n  { city: 'Beijing', timeZone: 'Asia/Shanghai' },\n  { city: 'Shanghai', timeZone: 'Asia/Shanghai' },\n  { city: 'Hong Kong', timeZone: 'Asia/Hong_Kong' },\n  { city: 'Singapore', timeZone: 'Asia/Singapore' },\n  { city: 'Seoul', timeZone: 'Asia/Seoul' },\n  { city: 'Mumbai', timeZone: 'Asia/Kolkata' },\n  { city: 'Delhi', timeZone: 'Asia/Kolkata' },\n  { city: 'Bangalore', timeZone: 'Asia/Kolkata' },\n  { city: 'Bangkok', timeZone: 'Asia/Bangkok' },\n  { city: 'Jakarta', timeZone: 'Asia/Jakarta' },\n  { city: 'Manila', timeZone: 'Asia/Manila' },\n  { city: 'Kuala Lumpur', timeZone: 'Asia/Kuala_Lumpur' },\n  { city: 'Dubai', timeZone: 'Asia/Dubai' },\n  { city: 'Tel Aviv', timeZone: 'Asia/Jerusalem' },\n  { city: 'Riyadh', timeZone: 'Asia/Riyadh' },\n  { city: 'Doha', timeZone: 'Asia/Qatar' },\n  { city: 'Kuwait City', timeZone: 'Asia/Kuwait' },\n  { city: 'Taipei', timeZone: 'Asia/Taipei' },\n  { city: 'Ho Chi Minh City', timeZone: 'Asia/Ho_Chi_Minh' },\n  { city: 'Hanoi', timeZone: 'Asia/Ho_Chi_Minh' },\n  { city: 'Yangon', timeZone: 'Asia/Yangon' },\n  { city: 'Dhaka', timeZone: 'Asia/Dhaka' },\n  { city: 'Karachi', timeZone: 'Asia/Karachi' },\n  { city: 'Islamabad', timeZone: 'Asia/Karachi' },\n\n  // Australia & Oceania\n  { city: 'Sydney', timeZone: 'Australia/Sydney' },\n  { city: 'Melbourne', timeZone: 'Australia/Melbourne' },\n  { city: 'Brisbane', timeZone: 'Australia/Brisbane' },\n  { city: 'Perth', timeZone: 'Australia/Perth' },\n  { city: 'Adelaide', timeZone: 'Australia/Adelaide' },\n  { city: 'Auckland', timeZone: 'Pacific/Auckland' },\n  { city: 'Wellington', timeZone: 'Pacific/Auckland' },\n  { city: 'Fiji', timeZone: 'Pacific/Fiji' },\n\n  // South America\n  { city: 'São Paulo', timeZone: 'America/Sao_Paulo' },\n  { city: 'Rio de Janeiro', timeZone: 'America/Sao_Paulo' },\n  { city: 'Buenos Aires', timeZone: 'America/Argentina/Buenos_Aires' },\n  { city: 'Santiago', timeZone: 'America/Santiago' },\n  { city: 'Lima', timeZone: 'America/Lima' },\n  { city: 'Bogotá', timeZone: 'America/Bogota' },\n  { city: 'Caracas', timeZone: 'America/Caracas' },\n  { city: 'Montevideo', timeZone: 'America/Montevideo' },\n\n  // Africa\n  { city: 'Cairo', timeZone: 'Africa/Cairo' },\n  { city: 'Lagos', timeZone: 'Africa/Lagos' },\n  { city: 'Johannesburg', timeZone: 'Africa/Johannesburg' },\n  { city: 'Cape Town', timeZone: 'Africa/Johannesburg' },\n  { city: 'Nairobi', timeZone: 'Africa/Nairobi' },\n  { city: 'Casablanca', timeZone: 'Africa/Casablanca' },\n  { city: 'Tunis', timeZone: 'Africa/Tunis' },\n  { city: 'Algiers', timeZone: 'Africa/Algiers' },\n  { city: 'Addis Ababa', timeZone: 'Africa/Addis_Ababa' },\n  { city: 'Accra', timeZone: 'Africa/Accra' },\n\n  // Additional major cities\n  { city: 'Reykjavik', timeZone: 'Atlantic/Reykjavik' },\n  { city: 'Azores', timeZone: 'Atlantic/Azores' },\n  { city: 'Canary Islands', timeZone: 'Atlantic/Canary' },\n  { city: 'Bermuda', timeZone: 'Atlantic/Bermuda' },\n  { city: 'Barbados', timeZone: 'America/Barbados' },\n  { city: 'Jamaica', timeZone: 'America/Jamaica' },\n  { city: 'Puerto Rico', timeZone: 'America/Puerto_Rico' },\n  { city: 'Guam', timeZone: 'Pacific/Guam' },\n  { city: 'Samoa', timeZone: 'Pacific/Apia' },\n  { city: 'Tahiti', timeZone: 'Pacific/Tahiti' },\n  { city: 'Tonga', timeZone: 'Pacific/Tongatapu' },\n  { city: 'Vanuatu', timeZone: 'Pacific/Efate' },\n  { city: 'New Caledonia', timeZone: 'Pacific/Noumea' },\n  { city: 'Solomon Islands', timeZone: 'Pacific/Guadalcanal' },\n  { city: 'Marshall Islands', timeZone: 'Pacific/Majuro' },\n  { city: 'Palau', timeZone: 'Pacific/Palau' },\n  { city: 'Micronesia', timeZone: 'Pacific/Chuuk' },\n  { city: 'Kiribati', timeZone: 'Pacific/Tarawa' },\n  { city: 'Cook Islands', timeZone: 'Pacific/Rarotonga' },\n  { city: 'Easter Island', timeZone: 'Pacific/Easter' },\n  { city: 'Galapagos', timeZone: 'Pacific/Galapagos' },\n  { city: 'Marquesas', timeZone: 'Pacific/Marquesas' },\n  { city: 'Pitcairn', timeZone: 'Pacific/Pitcairn' },\n  { city: 'Norfolk Island', timeZone: 'Pacific/Norfolk' },\n  { city: 'Lord Howe Island', timeZone: 'Australia/Lord_Howe' },\n  { city: 'Chatham Islands', timeZone: 'Pacific/Chatham' }\n];\n\n/**\n * Get timezone data by timezone identifier\n */\nexport function getTimezoneByIdentifier(timeZone: string) {\n  return timeZones.find(tz => tz.timeZone === timeZone);\n}\n\n/**\n * Search timezones by city name or timezone identifier\n */\nexport function searchTimezones(query: string, limit: number = 10) {\n  const lowercaseQuery = query.toLowerCase();\n  return timeZones\n    .filter(tz => \n      tz.city.toLowerCase().includes(lowercaseQuery) ||\n      tz.timeZone.toLowerCase().includes(lowercaseQuery)\n    )\n    .slice(0, limit);\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AACM,MAAM,YAAY;IACvB,gBAAgB;IAChB;QAAE,MAAM;QAAY,UAAU;IAAmB;IACjD;QAAE,MAAM;QAAe,UAAU;IAAsB;IACvD;QAAE,MAAM;QAAW,UAAU;IAAkB;IAC/C;QAAE,MAAM;QAAU,UAAU;IAAiB;IAC7C;QAAE,MAAM;QAAW,UAAU;IAAkB;IAC/C;QAAE,MAAM;QAAa,UAAU;IAAsB;IACrD;QAAE,MAAM;QAAS,UAAU;IAAmB;IAC9C;QAAE,MAAM;QAAW,UAAU;IAAsB;IACnD;QAAE,MAAM;QAAU,UAAU;IAAmB;IAC/C;QAAE,MAAM;QAAiB,UAAU;IAAsB;IACzD;QAAE,MAAM;QAAiB,UAAU;IAAmB;IACtD;QAAE,MAAM;QAAW,UAAU;IAAmB;IAChD;QAAE,MAAM;QAAU,UAAU;IAAkB;IAC9C;QAAE,MAAM;QAAW,UAAU;IAAkB;IAC/C;QAAE,MAAM;QAAgB,UAAU;IAAmB;IACrD;QAAE,MAAM;QAAW,UAAU;IAAkB;IAC/C;QAAE,MAAM;QAAa,UAAU;IAAoB;IACnD;QAAE,MAAM;QAAY,UAAU;IAAmB;IACjD;QAAE,MAAM;QAAW,UAAU;IAAmB;IAChD;QAAE,MAAM;QAAe,UAAU;IAAsB;IACvD;QAAE,MAAM;QAAa,UAAU;IAAoB;IACnD;QAAE,MAAM;QAAY,UAAU;IAAmB;IAEjD,SAAS;IACT;QAAE,MAAM;QAAU,UAAU;IAAgB;IAC5C;QAAE,MAAM;QAAS,UAAU;IAAe;IAC1C;QAAE,MAAM;QAAU,UAAU;IAAgB;IAC5C;QAAE,MAAM;QAAQ,UAAU;IAAc;IACxC;QAAE,MAAM;QAAU,UAAU;IAAgB;IAC5C;QAAE,MAAM;QAAa,UAAU;IAAmB;IAClD;QAAE,MAAM;QAAY,UAAU;IAAkB;IAChD;QAAE,MAAM;QAAU,UAAU;IAAgB;IAC5C;QAAE,MAAM;QAAU,UAAU;IAAgB;IAC5C;QAAE,MAAM;QAAa,UAAU;IAAmB;IAClD;QAAE,MAAM;QAAQ,UAAU;IAAc;IACxC;QAAE,MAAM;QAAc,UAAU;IAAoB;IACpD;QAAE,MAAM;QAAY,UAAU;IAAkB;IAChD;QAAE,MAAM;QAAU,UAAU;IAAgB;IAC5C;QAAE,MAAM;QAAU,UAAU;IAAgB;IAC5C;QAAE,MAAM;QAAY,UAAU;IAAkB;IAChD;QAAE,MAAM;QAAU,UAAU;IAAgB;IAC5C;QAAE,MAAM;QAAY,UAAU;IAAkB;IAChD;QAAE,MAAM;QAAU,UAAU;IAAgB;IAC5C;QAAE,MAAM;QAAU,UAAU;IAAgB;IAC5C;QAAE,MAAM;QAAU,UAAU;IAAgB;IAC5C;QAAE,MAAM;QAAa,UAAU;IAAgB;IAC/C;QAAE,MAAM;QAAU,UAAU;IAAgB;IAC5C;QAAE,MAAM;QAAS,UAAU;IAAc;IAEzC,OAAO;IACP;QAAE,MAAM;QAAS,UAAU;IAAa;IACxC;QAAE,MAAM;QAAW,UAAU;IAAgB;IAC7C;QAAE,MAAM;QAAY,UAAU;IAAgB;IAC9C;QAAE,MAAM;QAAa,UAAU;IAAiB;IAChD;QAAE,MAAM;QAAa,UAAU;IAAiB;IAChD;QAAE,MAAM;QAAS,UAAU;IAAa;IACxC;QAAE,MAAM;QAAU,UAAU;IAAe;IAC3C;QAAE,MAAM;QAAS,UAAU;IAAe;IAC1C;QAAE,MAAM;QAAa,UAAU;IAAe;IAC9C;QAAE,MAAM;QAAW,UAAU;IAAe;IAC5C;QAAE,MAAM;QAAW,UAAU;IAAe;IAC5C;QAAE,MAAM;QAAU,UAAU;IAAc;IAC1C;QAAE,MAAM;QAAgB,UAAU;IAAoB;IACtD;QAAE,MAAM;QAAS,UAAU;IAAa;IACxC;QAAE,MAAM;QAAY,UAAU;IAAiB;IAC/C;QAAE,MAAM;QAAU,UAAU;IAAc;IAC1C;QAAE,MAAM;QAAQ,UAAU;IAAa;IACvC;QAAE,MAAM;QAAe,UAAU;IAAc;IAC/C;QAAE,MAAM;QAAU,UAAU;IAAc;IAC1C;QAAE,MAAM;QAAoB,UAAU;IAAmB;IACzD;QAAE,MAAM;QAAS,UAAU;IAAmB;IAC9C;QAAE,MAAM;QAAU,UAAU;IAAc;IAC1C;QAAE,MAAM;QAAS,UAAU;IAAa;IACxC;QAAE,MAAM;QAAW,UAAU;IAAe;IAC5C;QAAE,MAAM;QAAa,UAAU;IAAe;IAE9C,sBAAsB;IACtB;QAAE,MAAM;QAAU,UAAU;IAAmB;IAC/C;QAAE,MAAM;QAAa,UAAU;IAAsB;IACrD;QAAE,MAAM;QAAY,UAAU;IAAqB;IACnD;QAAE,MAAM;QAAS,UAAU;IAAkB;IAC7C;QAAE,MAAM;QAAY,UAAU;IAAqB;IACnD;QAAE,MAAM;QAAY,UAAU;IAAmB;IACjD;QAAE,MAAM;QAAc,UAAU;IAAmB;IACnD;QAAE,MAAM;QAAQ,UAAU;IAAe;IAEzC,gBAAgB;IAChB;QAAE,MAAM;QAAa,UAAU;IAAoB;IACnD;QAAE,MAAM;QAAkB,UAAU;IAAoB;IACxD;QAAE,MAAM;QAAgB,UAAU;IAAiC;IACnE;QAAE,MAAM;QAAY,UAAU;IAAmB;IACjD;QAAE,MAAM;QAAQ,UAAU;IAAe;IACzC;QAAE,MAAM;QAAU,UAAU;IAAiB;IAC7C;QAAE,MAAM;QAAW,UAAU;IAAkB;IAC/C;QAAE,MAAM;QAAc,UAAU;IAAqB;IAErD,SAAS;IACT;QAAE,MAAM;QAAS,UAAU;IAAe;IAC1C;QAAE,MAAM;QAAS,UAAU;IAAe;IAC1C;QAAE,MAAM;QAAgB,UAAU;IAAsB;IACxD;QAAE,MAAM;QAAa,UAAU;IAAsB;IACrD;QAAE,MAAM;QAAW,UAAU;IAAiB;IAC9C;QAAE,MAAM;QAAc,UAAU;IAAoB;IACpD;QAAE,MAAM;QAAS,UAAU;IAAe;IAC1C;QAAE,MAAM;QAAW,UAAU;IAAiB;IAC9C;QAAE,MAAM;QAAe,UAAU;IAAqB;IACtD;QAAE,MAAM;QAAS,UAAU;IAAe;IAE1C,0BAA0B;IAC1B;QAAE,MAAM;QAAa,UAAU;IAAqB;IACpD;QAAE,MAAM;QAAU,UAAU;IAAkB;IAC9C;QAAE,MAAM;QAAkB,UAAU;IAAkB;IACtD;QAAE,MAAM;QAAW,UAAU;IAAmB;IAChD;QAAE,MAAM;QAAY,UAAU;IAAmB;IACjD;QAAE,MAAM;QAAW,UAAU;IAAkB;IAC/C;QAAE,MAAM;QAAe,UAAU;IAAsB;IACvD;QAAE,MAAM;QAAQ,UAAU;IAAe;IACzC;QAAE,MAAM;QAAS,UAAU;IAAe;IAC1C;QAAE,MAAM;QAAU,UAAU;IAAiB;IAC7C;QAAE,MAAM;QAAS,UAAU;IAAoB;IAC/C;QAAE,MAAM;QAAW,UAAU;IAAgB;IAC7C;QAAE,MAAM;QAAiB,UAAU;IAAiB;IACpD;QAAE,MAAM;QAAmB,UAAU;IAAsB;IAC3D;QAAE,MAAM;QAAoB,UAAU;IAAiB;IACvD;QAAE,MAAM;QAAS,UAAU;IAAgB;IAC3C;QAAE,MAAM;QAAc,UAAU;IAAgB;IAChD;QAAE,MAAM;QAAY,UAAU;IAAiB;IAC/C;QAAE,MAAM;QAAgB,UAAU;IAAoB;IACtD;QAAE,MAAM;QAAiB,UAAU;IAAiB;IACpD;QAAE,MAAM;QAAa,UAAU;IAAoB;IACnD;QAAE,MAAM;QAAa,UAAU;IAAoB;IACnD;QAAE,MAAM;QAAY,UAAU;IAAmB;IACjD;QAAE,MAAM;QAAkB,UAAU;IAAkB;IACtD;QAAE,MAAM;QAAoB,UAAU;IAAsB;IAC5D;QAAE,MAAM;QAAmB,UAAU;IAAkB;CACxD;AAKM,SAAS,wBAAwB,QAAgB;IACtD,OAAO,UAAU,IAAI,CAAC,CAAA,KAAM,GAAG,QAAQ,KAAK;AAC9C;AAKO,SAAS,gBAAgB,KAAa,EAAE,QAAgB,EAAE;IAC/D,MAAM,iBAAiB,MAAM,WAAW;IACxC,OAAO,UACJ,MAAM,CAAC,CAAA,KACN,GAAG,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,mBAC/B,GAAG,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,iBAEpC,KAAK,CAAC,GAAG;AACd", "debugId": null}}, {"offset": {"line": 694, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/src/app/components/CitySearchInput.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { timeZones } from '../utils/timezones';\n\ninterface CitySearchInputProps {\n  onCitySelect: (city: { city: string; timeZone: string; id: string }) => void;\n}\n\nexport default function CitySearchInput({ onCitySelect }: CitySearchInputProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [suggestions, setSuggestions] = useState<typeof timeZones>([]);\n  const [isOpen, setIsOpen] = useState(false);\n  const [selectedIndex, setSelectedIndex] = useState(-1);\n  const inputRef = useRef<HTMLInputElement>(null);\n  const listRef = useRef<HTMLUListElement>(null);\n\n  // Filter suggestions based on search term\n  useEffect(() => {\n    if (searchTerm.length < 2) {\n      setSuggestions([]);\n      setIsOpen(false);\n      return;\n    }\n\n    const filtered = timeZones\n      .filter(tz => \n        tz.city.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        tz.timeZone.toLowerCase().includes(searchTerm.toLowerCase())\n      )\n      .slice(0, 8); // Limit to 8 suggestions for performance\n\n    setSuggestions(filtered);\n    setIsOpen(filtered.length > 0);\n    setSelectedIndex(-1);\n  }, [searchTerm]);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setSearchTerm(e.target.value);\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (!isOpen) return;\n\n    switch (e.key) {\n      case 'ArrowDown':\n        e.preventDefault();\n        setSelectedIndex(prev => \n          prev < suggestions.length - 1 ? prev + 1 : prev\n        );\n        break;\n      case 'ArrowUp':\n        e.preventDefault();\n        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);\n        break;\n      case 'Enter':\n        e.preventDefault();\n        if (selectedIndex >= 0 && suggestions[selectedIndex]) {\n          handleCitySelect(suggestions[selectedIndex]);\n        }\n        break;\n      case 'Escape':\n        setIsOpen(false);\n        setSelectedIndex(-1);\n        inputRef.current?.blur();\n        break;\n    }\n  };\n\n  const handleCitySelect = (city: typeof timeZones[0]) => {\n    const newCity = {\n      ...city,\n      id: `${city.timeZone}-${Date.now()}`\n    };\n    onCitySelect(newCity);\n    setSearchTerm('');\n    setIsOpen(false);\n    setSelectedIndex(-1);\n    inputRef.current?.focus();\n  };\n\n  const handleClickOutside = (e: MouseEvent) => {\n    if (\n      inputRef.current && \n      !inputRef.current.contains(e.target as Node) &&\n      listRef.current && \n      !listRef.current.contains(e.target as Node)\n    ) {\n      setIsOpen(false);\n      setSelectedIndex(-1);\n    }\n  };\n\n  useEffect(() => {\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  return (\n    <div className=\"relative w-full max-w-md mx-auto\">\n      <div className=\"relative\">\n        <input\n          ref={inputRef}\n          type=\"text\"\n          value={searchTerm}\n          onChange={handleInputChange}\n          onKeyDown={handleKeyDown}\n          placeholder=\"Search for a city or timezone...\"\n          className=\"w-full px-4 py-3 pl-10 bg-card border border-border rounded-lg \n                   text-foreground placeholder-muted-foreground\n                   focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent\n                   transition-colors\"\n          aria-label=\"Search for city or timezone\"\n          aria-expanded={isOpen}\n          aria-haspopup=\"listbox\"\n          role=\"combobox\"\n        />\n        <svg \n          className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground\"\n          fill=\"none\" \n          stroke=\"currentColor\" \n          viewBox=\"0 0 24 24\"\n        >\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n        </svg>\n      </div>\n\n      {isOpen && suggestions.length > 0 && (\n        <ul\n          ref={listRef}\n          className=\"absolute z-50 w-full mt-1 bg-card border border-border rounded-lg shadow-lg max-h-64 overflow-y-auto\"\n          role=\"listbox\"\n        >\n          {suggestions.map((suggestion, index) => (\n            <li\n              key={`${suggestion.timeZone}-${index}`}\n              className={`px-4 py-3 cursor-pointer transition-colors border-b border-border last:border-b-0 ${\n                index === selectedIndex\n                  ? 'bg-accent text-accent-foreground'\n                  : 'hover:bg-accent hover:text-accent-foreground'\n              }`}\n              onClick={() => handleCitySelect(suggestion)}\n              role=\"option\"\n              aria-selected={index === selectedIndex}\n            >\n              <div className=\"flex justify-between items-center\">\n                <span className=\"font-medium\">{suggestion.city}</span>\n                <span className=\"text-sm text-muted-foreground\">\n                  {suggestion.timeZone.split('/').pop()?.replace('_', ' ')}\n                </span>\n              </div>\n            </li>\n          ))}\n        </ul>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AASe,SAAS,gBAAgB,EAAE,YAAY,EAAwB;;IAC5E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACnE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACpD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAEzC,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,WAAW,MAAM,GAAG,GAAG;gBACzB,eAAe,EAAE;gBACjB,UAAU;gBACV;YACF;YAEA,MAAM,WAAW,mIAAA,CAAA,YAAS,CACvB,MAAM;sDAAC,CAAA,KACN,GAAG,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACrD,GAAG,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;qDAE1D,KAAK,CAAC,GAAG,IAAI,yCAAyC;YAEzD,eAAe;YACf,UAAU,SAAS,MAAM,GAAG;YAC5B,iBAAiB,CAAC;QACpB;oCAAG;QAAC;KAAW;IAEf,MAAM,oBAAoB,CAAC;QACzB,cAAc,EAAE,MAAM,CAAC,KAAK;IAC9B;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,QAAQ;QAEb,OAAQ,EAAE,GAAG;YACX,KAAK;gBACH,EAAE,cAAc;gBAChB,iBAAiB,CAAA,OACf,OAAO,YAAY,MAAM,GAAG,IAAI,OAAO,IAAI;gBAE7C;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,iBAAiB,CAAA,OAAQ,OAAO,IAAI,OAAO,IAAI,CAAC;gBAChD;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,IAAI,iBAAiB,KAAK,WAAW,CAAC,cAAc,EAAE;oBACpD,iBAAiB,WAAW,CAAC,cAAc;gBAC7C;gBACA;YACF,KAAK;gBACH,UAAU;gBACV,iBAAiB,CAAC;gBAClB,SAAS,OAAO,EAAE;gBAClB;QACJ;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,UAAU;YACd,GAAG,IAAI;YACP,IAAI,GAAG,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;QACtC;QACA,aAAa;QACb,cAAc;QACd,UAAU;QACV,iBAAiB,CAAC;QAClB,SAAS,OAAO,EAAE;IACpB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IACE,SAAS,OAAO,IAChB,CAAC,SAAS,OAAO,CAAC,QAAQ,CAAC,EAAE,MAAM,KACnC,QAAQ,OAAO,IACf,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,EAAE,MAAM,GAClC;YACA,UAAU;YACV,iBAAiB,CAAC;QACpB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,SAAS,gBAAgB,CAAC,aAAa;YACvC;6CAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;oCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,WAAW;wBACX,aAAY;wBACZ,WAAU;wBAIV,cAAW;wBACX,iBAAe;wBACf,iBAAc;wBACd,MAAK;;;;;;kCAEP,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;YAIxE,UAAU,YAAY,MAAM,GAAG,mBAC9B,6LAAC;gBACC,KAAK;gBACL,WAAU;gBACV,MAAK;0BAEJ,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,6LAAC;wBAEC,WAAW,CAAC,kFAAkF,EAC5F,UAAU,gBACN,qCACA,gDACJ;wBACF,SAAS,IAAM,iBAAiB;wBAChC,MAAK;wBACL,iBAAe,UAAU;kCAEzB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAe,WAAW,IAAI;;;;;;8CAC9C,6LAAC;oCAAK,WAAU;8CACb,WAAW,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,QAAQ,KAAK;;;;;;;;;;;;uBAbnD,GAAG,WAAW,QAAQ,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;;AAsBpD;GApJwB;KAAA", "debugId": null}}, {"offset": {"line": 896, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/src/app/components/TimeZoneCard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { format, parseISO } from 'date-fns';\nimport { zonedTimeToUtc, utcToZonedTime } from 'date-fns-tz';\n\ninterface TimeZoneCardProps {\n  city: string;\n  timeZone: string;\n  id: string;\n  timeFormat: '12h' | '24h';\n  referenceTime: Date | null;\n  onTimeChange: (newTime: Date) => void;\n  onRemove: (id: string) => void;\n}\n\nexport default function TimeZoneCard({\n  city,\n  timeZone,\n  id,\n  timeFormat,\n  referenceTime,\n  onTimeChange,\n  onRemove\n}: TimeZoneCardProps) {\n  const [currentTime, setCurrentTime] = useState<Date>(new Date());\n  const [isEditing, setIsEditing] = useState(false);\n  const [editTime, setEditTime] = useState('');\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  // Update current time every second or when reference time changes\n  useEffect(() => {\n    const updateTime = () => {\n      if (referenceTime) {\n        // Use reference time to calculate this timezone's time\n        const zonedTime = utcToZonedTime(referenceTime, timeZone);\n        setCurrentTime(zonedTime);\n      } else {\n        // Use current time\n        const now = new Date();\n        const zonedTime = utcToZonedTime(now, timeZone);\n        setCurrentTime(zonedTime);\n      }\n    };\n\n    updateTime();\n    const interval = setInterval(updateTime, 1000);\n    return () => clearInterval(interval);\n  }, [timeZone, referenceTime]);\n\n  // Format time based on selected format\n  const formatTime = (date: Date) => {\n    try {\n      if (timeFormat === '12h') {\n        return format(date, 'h:mm:ss a');\n      } else {\n        return format(date, 'HH:mm:ss');\n      }\n    } catch (error) {\n      console.error('Error formatting time:', error);\n      return '00:00:00';\n    }\n  };\n\n  // Format date\n  const formatDate = (date: Date) => {\n    try {\n      return format(date, 'EEEE, MMMM d, yyyy');\n    } catch (error) {\n      console.error('Error formatting date:', error);\n      return '';\n    }\n  };\n\n  // Get timezone abbreviation\n  const getTimezoneAbbr = () => {\n    try {\n      return new Intl.DateTimeFormat('en', {\n        timeZone,\n        timeZoneName: 'short'\n      }).formatToParts(currentTime)\n        .find(part => part.type === 'timeZoneName')?.value || timeZone.split('/').pop();\n    } catch (error) {\n      return timeZone.split('/').pop();\n    }\n  };\n\n  const handleTimeClick = () => {\n    setIsEditing(true);\n    setEditTime(format(currentTime, timeFormat === '12h' ? 'h:mm a' : 'HH:mm'));\n  };\n\n  const handleTimeSubmit = () => {\n    try {\n      let newTime: Date;\n\n      if (timeFormat === '12h') {\n        // Parse 12-hour format (e.g., \"2:30 PM\")\n        const trimmedTime = editTime.trim();\n        const match = trimmedTime.match(/^(\\d{1,2}):(\\d{2})\\s*(AM|PM|am|pm)$/i);\n\n        if (!match) {\n          console.error('Invalid 12-hour time format');\n          setIsEditing(false);\n          return;\n        }\n\n        let hours = parseInt(match[1], 10);\n        const minutes = parseInt(match[2], 10);\n        const period = match[3].toUpperCase();\n\n        if (hours < 1 || hours > 12 || minutes < 0 || minutes > 59) {\n          console.error('Invalid time values');\n          setIsEditing(false);\n          return;\n        }\n\n        if (period === 'PM' && hours !== 12) {\n          hours += 12;\n        } else if (period === 'AM' && hours === 12) {\n          hours = 0;\n        }\n\n        newTime = new Date(currentTime);\n        newTime.setHours(hours, minutes, 0, 0);\n      } else {\n        // Parse 24-hour format (e.g., \"14:30\")\n        const match = editTime.trim().match(/^(\\d{1,2}):(\\d{2})$/);\n\n        if (!match) {\n          console.error('Invalid 24-hour time format');\n          setIsEditing(false);\n          return;\n        }\n\n        const hours = parseInt(match[1], 10);\n        const minutes = parseInt(match[2], 10);\n\n        if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {\n          console.error('Invalid time values');\n          setIsEditing(false);\n          return;\n        }\n\n        newTime = new Date(currentTime);\n        newTime.setHours(hours, minutes, 0, 0);\n      }\n\n      // Convert to UTC for reference\n      const utcTime = zonedTimeToUtc(newTime, timeZone);\n      onTimeChange(utcTime);\n\n      setIsEditing(false);\n    } catch (error) {\n      console.error('Error parsing time:', error);\n      setIsEditing(false);\n    }\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      handleTimeSubmit();\n    } else if (e.key === 'Escape') {\n      setIsEditing(false);\n    }\n  };\n\n  useEffect(() => {\n    if (isEditing && inputRef.current) {\n      inputRef.current.focus();\n      inputRef.current.select();\n    }\n  }, [isEditing]);\n\n  return (\n    <div className=\"bg-card border border-border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow\">\n      <div className=\"flex justify-between items-start mb-3\">\n        <div className=\"flex-1\">\n          <h3 className=\"text-lg font-semibold text-foreground\">{city}</h3>\n          <p className=\"text-sm text-muted-foreground\">{getTimezoneAbbr()}</p>\n        </div>\n        <button\n          onClick={() => onRemove(id)}\n          className=\"p-1 text-muted-foreground hover:text-destructive transition-colors\"\n          aria-label={`Remove ${city}`}\n        >\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n          </svg>\n        </button>\n      </div>\n\n      <div className=\"space-y-2\">\n        <p className=\"text-sm text-muted-foreground\">{formatDate(currentTime)}</p>\n\n        {isEditing ? (\n          <input\n            ref={inputRef}\n            type=\"text\"\n            value={editTime}\n            onChange={(e) => setEditTime(e.target.value)}\n            onBlur={handleTimeSubmit}\n            onKeyDown={handleKeyDown}\n            className=\"text-2xl font-mono font-bold bg-input border border-border rounded px-2 py-1 w-full focus:outline-none focus:ring-2 focus:ring-ring\"\n            placeholder={timeFormat === '12h' ? '12:00 PM' : '12:00'}\n          />\n        ) : (\n          <button\n            onClick={handleTimeClick}\n            className=\"text-2xl font-mono font-bold text-foreground hover:text-primary transition-colors cursor-pointer\"\n            aria-label={`Edit time for ${city}. Current time: ${formatTime(currentTime)}`}\n          >\n            {formatTime(currentTime)}\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAgBe,SAAS,aAAa,EACnC,IAAI,EACJ,QAAQ,EACR,EAAE,EACF,UAAU,EACV,aAAa,EACb,YAAY,EACZ,QAAQ,EACU;;IAClB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAQ,IAAI;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,kEAAkE;IAClE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;qDAAa;oBACjB,IAAI,eAAe;wBACjB,uDAAuD;wBACvD,MAAM,YAAY,CAAA,GAAA,6KAAA,CAAA,iBAAc,AAAD,EAAE,eAAe;wBAChD,eAAe;oBACjB,OAAO;wBACL,mBAAmB;wBACnB,MAAM,MAAM,IAAI;wBAChB,MAAM,YAAY,CAAA,GAAA,6KAAA,CAAA,iBAAc,AAAD,EAAE,KAAK;wBACtC,eAAe;oBACjB;gBACF;;YAEA;YACA,MAAM,WAAW,YAAY,YAAY;YACzC;0CAAO,IAAM,cAAc;;QAC7B;iCAAG;QAAC;QAAU;KAAc;IAE5B,uCAAuC;IACvC,MAAM,aAAa,CAAC;QAClB,IAAI;YACF,IAAI,eAAe,OAAO;gBACxB,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;YACtB,OAAO;gBACL,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA,cAAc;IACd,MAAM,aAAa,CAAC;QAClB,IAAI;YACF,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,MAAM,kBAAkB;QACtB,IAAI;YACF,OAAO,IAAI,KAAK,cAAc,CAAC,MAAM;gBACnC;gBACA,cAAc;YAChB,GAAG,aAAa,CAAC,aACd,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,iBAAiB,SAAS,SAAS,KAAK,CAAC,KAAK,GAAG;QACjF,EAAE,OAAO,OAAO;YACd,OAAO,SAAS,KAAK,CAAC,KAAK,GAAG;QAChC;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa;QACb,YAAY,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,aAAa,eAAe,QAAQ,WAAW;IACpE;IAEA,MAAM,mBAAmB;QACvB,IAAI;YACF,IAAI;YAEJ,IAAI,eAAe,OAAO;gBACxB,yCAAyC;gBACzC,MAAM,cAAc,SAAS,IAAI;gBACjC,MAAM,QAAQ,YAAY,KAAK,CAAC;gBAEhC,IAAI,CAAC,OAAO;oBACV,QAAQ,KAAK,CAAC;oBACd,aAAa;oBACb;gBACF;gBAEA,IAAI,QAAQ,SAAS,KAAK,CAAC,EAAE,EAAE;gBAC/B,MAAM,UAAU,SAAS,KAAK,CAAC,EAAE,EAAE;gBACnC,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC,WAAW;gBAEnC,IAAI,QAAQ,KAAK,QAAQ,MAAM,UAAU,KAAK,UAAU,IAAI;oBAC1D,QAAQ,KAAK,CAAC;oBACd,aAAa;oBACb;gBACF;gBAEA,IAAI,WAAW,QAAQ,UAAU,IAAI;oBACnC,SAAS;gBACX,OAAO,IAAI,WAAW,QAAQ,UAAU,IAAI;oBAC1C,QAAQ;gBACV;gBAEA,UAAU,IAAI,KAAK;gBACnB,QAAQ,QAAQ,CAAC,OAAO,SAAS,GAAG;YACtC,OAAO;gBACL,uCAAuC;gBACvC,MAAM,QAAQ,SAAS,IAAI,GAAG,KAAK,CAAC;gBAEpC,IAAI,CAAC,OAAO;oBACV,QAAQ,KAAK,CAAC;oBACd,aAAa;oBACb;gBACF;gBAEA,MAAM,QAAQ,SAAS,KAAK,CAAC,EAAE,EAAE;gBACjC,MAAM,UAAU,SAAS,KAAK,CAAC,EAAE,EAAE;gBAEnC,IAAI,QAAQ,KAAK,QAAQ,MAAM,UAAU,KAAK,UAAU,IAAI;oBAC1D,QAAQ,KAAK,CAAC;oBACd,aAAa;oBACb;gBACF;gBAEA,UAAU,IAAI,KAAK;gBACnB,QAAQ,QAAQ,CAAC,OAAO,SAAS,GAAG;YACtC;YAEA,+BAA+B;YAC/B,MAAM,UAAU,CAAA,GAAA,6KAAA,CAAA,iBAAc,AAAD,EAAE,SAAS;YACxC,aAAa;YAEb,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;YAC7B,aAAa;QACf;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,aAAa,SAAS,OAAO,EAAE;gBACjC,SAAS,OAAO,CAAC,KAAK;gBACtB,SAAS,OAAO,CAAC,MAAM;YACzB;QACF;iCAAG;QAAC;KAAU;IAEd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAiC;;;;;;;;;;;;kCAEhD,6LAAC;wBACC,SAAS,IAAM,SAAS;wBACxB,WAAU;wBACV,cAAY,CAAC,OAAO,EAAE,MAAM;kCAE5B,cAAA,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAK3E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAiC,WAAW;;;;;;oBAExD,0BACC,6LAAC;wBACC,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wBAC3C,QAAQ;wBACR,WAAW;wBACX,WAAU;wBACV,aAAa,eAAe,QAAQ,aAAa;;;;;6CAGnD,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAY,CAAC,cAAc,EAAE,KAAK,gBAAgB,EAAE,WAAW,cAAc;kCAE5E,WAAW;;;;;;;;;;;;;;;;;;AAMxB;GA1MwB;KAAA", "debugId": null}}, {"offset": {"line": 1177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/src/app/components/TimeZoneList.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport TimeZoneCard from './TimeZoneCard';\n\ninterface TimeZoneSelection {\n  city: string;\n  timeZone: string;\n  id: string;\n}\n\ninterface TimeZoneListProps {\n  timeZones: TimeZoneSelection[];\n  timeFormat: '12h' | '24h';\n  referenceTime: Date | null;\n  onTimeChange: (newTime: Date) => void;\n  onRemoveTimeZone: (id: string) => void;\n}\n\nexport default function TimeZoneList({\n  timeZones,\n  timeFormat,\n  referenceTime,\n  onTimeChange,\n  onRemoveTimeZone\n}: TimeZoneListProps) {\n  if (timeZones.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"max-w-md mx-auto\">\n          <svg \n            className=\"w-16 h-16 mx-auto text-muted-foreground mb-4\" \n            fill=\"none\" \n            stroke=\"currentColor\" \n            viewBox=\"0 0 24 24\"\n          >\n            <path \n              strokeLinecap=\"round\" \n              strokeLinejoin=\"round\" \n              strokeWidth={1.5} \n              d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" \n            />\n          </svg>\n          <h3 className=\"text-lg font-medium text-foreground mb-2\">\n            No time zones added yet\n          </h3>\n          <p className=\"text-muted-foreground\">\n            Search for a city above to add your first time zone and start converting times.\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n      {timeZones.map((tz) => (\n        <TimeZoneCard\n          key={tz.id}\n          city={tz.city}\n          timeZone={tz.timeZone}\n          id={tz.id}\n          timeFormat={timeFormat}\n          referenceTime={referenceTime}\n          onTimeChange={onTimeChange}\n          onRemove={onRemoveTimeZone}\n        />\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAmBe,SAAS,aAAa,EACnC,SAAS,EACT,UAAU,EACV,aAAa,EACb,YAAY,EACZ,gBAAgB,EACE;IAClB,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,6LAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,aAAa;4BACb,GAAE;;;;;;;;;;;kCAGN,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAM7C;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,UAAU,GAAG,CAAC,CAAC,mBACd,6LAAC,4IAAA,CAAA,UAAY;gBAEX,MAAM,GAAG,IAAI;gBACb,UAAU,GAAG,QAAQ;gBACrB,IAAI,GAAG,EAAE;gBACT,YAAY;gBACZ,eAAe;gBACf,cAAc;gBACd,UAAU;eAPL,GAAG,EAAE;;;;;;;;;;AAYpB;KAnDwB", "debugId": null}}, {"offset": {"line": 1273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/src/app/hooks/useLocalStorage.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\n/**\n * Custom hook for managing localStorage with React state\n * Provides automatic synchronization between localStorage and component state\n */\nexport function useLocalStorage<T>(\n  key: string,\n  initialValue: T\n): [T, (value: T | ((val: T) => T)) => void] {\n  // State to store our value\n  // Pass initial state function to useState so logic is only executed once\n  const [storedValue, setStoredValue] = useState<T>(() => {\n    if (typeof window === 'undefined') {\n      return initialValue;\n    }\n    \n    try {\n      // Get from local storage by key\n      const item = window.localStorage.getItem(key);\n      // Parse stored json or if none return initialValue\n      return item ? JSON.parse(item) : initialValue;\n    } catch (error) {\n      // If error also return initialValue\n      console.error(`Error reading localStorage key \"${key}\":`, error);\n      return initialValue;\n    }\n  });\n\n  // Return a wrapped version of useState's setter function that ...\n  // ... persists the new value to localStorage.\n  const setValue = (value: T | ((val: T) => T)) => {\n    try {\n      // Allow value to be a function so we have the same API as useState\n      const valueToStore = value instanceof Function ? value(storedValue) : value;\n      \n      // Save state\n      setStoredValue(valueToStore);\n      \n      // Save to local storage\n      if (typeof window !== 'undefined') {\n        window.localStorage.setItem(key, JSON.stringify(valueToStore));\n      }\n    } catch (error) {\n      // A more advanced implementation would handle the error case\n      console.error(`Error setting localStorage key \"${key}\":`, error);\n    }\n  };\n\n  return [storedValue, setValue];\n}\n\n/**\n * Hook for managing theme preference in localStorage\n */\nexport function useTheme() {\n  const [theme, setTheme] = useLocalStorage<'light' | 'dark'>('theme', 'light');\n\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const root = window.document.documentElement;\n      \n      // Remove previous theme classes\n      root.classList.remove('light', 'dark');\n      \n      // Add current theme class\n      root.classList.add(theme);\n    }\n  }, [theme]);\n\n  return [theme, setTheme] as const;\n}\n\n/**\n * Hook for managing time format preference in localStorage\n */\nexport function useTimeFormat() {\n  const [timeFormat, setTimeFormat] = useLocalStorage<'12h' | '24h'>('timeFormat', '12h');\n  return [timeFormat, setTimeFormat] as const;\n}\n\n/**\n * Interface for timezone selection data\n */\nexport interface TimeZoneSelection {\n  city: string;\n  timeZone: string;\n  id: string;\n}\n\n/**\n * Hook for managing selected timezones in localStorage\n */\nexport function useTimeZones() {\n  const [timeZones, setTimeZones] = useLocalStorage<TimeZoneSelection[]>('selectedTimeZones', []);\n\n  const addTimeZone = (timeZone: TimeZoneSelection) => {\n    setTimeZones(prev => {\n      // Check if timezone already exists\n      const exists = prev.some(tz => tz.timeZone === timeZone.timeZone);\n      if (exists) {\n        return prev;\n      }\n      return [...prev, timeZone];\n    });\n  };\n\n  const removeTimeZone = (id: string) => {\n    setTimeZones(prev => prev.filter(tz => tz.id !== id));\n  };\n\n  const clearTimeZones = () => {\n    setTimeZones([]);\n  };\n\n  return {\n    timeZones,\n    addTimeZone,\n    removeTimeZone,\n    clearTimeZones,\n    setTimeZones\n  };\n}\n"], "names": [], "mappings": ";;;;;;AAEA;;AAFA;;AAQO,SAAS,gBACd,GAAW,EACX,YAAe;;IAEf,2BAA2B;IAC3B,yEAAyE;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;oCAAK;YAChD,uCAAmC;;YAEnC;YAEA,IAAI;gBACF,gCAAgC;gBAChC,MAAM,OAAO,OAAO,YAAY,CAAC,OAAO,CAAC;gBACzC,mDAAmD;gBACnD,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;YACnC,EAAE,OAAO,OAAO;gBACd,oCAAoC;gBACpC,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,IAAI,EAAE,CAAC,EAAE;gBAC1D,OAAO;YACT;QACF;;IAEA,kEAAkE;IAClE,8CAA8C;IAC9C,MAAM,WAAW,CAAC;QAChB,IAAI;YACF,mEAAmE;YACnE,MAAM,eAAe,iBAAiB,WAAW,MAAM,eAAe;YAEtE,aAAa;YACb,eAAe;YAEf,wBAAwB;YACxB,wCAAmC;gBACjC,OAAO,YAAY,CAAC,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;YAClD;QACF,EAAE,OAAO,OAAO;YACd,6DAA6D;YAC7D,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,IAAI,EAAE,CAAC,EAAE;QAC5D;IACF;IAEA,OAAO;QAAC;QAAa;KAAS;AAChC;GA5CgB;AAiDT,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,gBAAkC,SAAS;IAErE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,wCAAmC;gBACjC,MAAM,OAAO,OAAO,QAAQ,CAAC,eAAe;gBAE5C,gCAAgC;gBAChC,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS;gBAE/B,0BAA0B;gBAC1B,KAAK,SAAS,CAAC,GAAG,CAAC;YACrB;QACF;6BAAG;QAAC;KAAM;IAEV,OAAO;QAAC;QAAO;KAAS;AAC1B;IAhBgB;;QACY;;;AAoBrB,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,gBAA+B,cAAc;IACjF,OAAO;QAAC;QAAY;KAAc;AACpC;IAHgB;;QACsB;;;AAgB/B,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,gBAAqC,qBAAqB,EAAE;IAE9F,MAAM,cAAc,CAAC;QACnB,aAAa,CAAA;YACX,mCAAmC;YACnC,MAAM,SAAS,KAAK,IAAI,CAAC,CAAA,KAAM,GAAG,QAAQ,KAAK,SAAS,QAAQ;YAChE,IAAI,QAAQ;gBACV,OAAO;YACT;YACA,OAAO;mBAAI;gBAAM;aAAS;QAC5B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,aAAa,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK;IACnD;IAEA,MAAM,iBAAiB;QACrB,aAAa,EAAE;IACjB;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;IA7BgB;;QACoB", "debugId": null}}, {"offset": {"line": 1410, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/src/app/utils/time.ts"], "sourcesContent": ["import { format, parseISO } from 'date-fns';\nimport { zonedTimeToUtc, utcToZonedTime } from 'date-fns-tz';\n\n/**\n * Get the user's local timezone\n */\nexport function getUserTimezone(): string {\n  try {\n    return Intl.DateTimeFormat().resolvedOptions().timeZone;\n  } catch (error) {\n    console.error('Error getting user timezone:', error);\n    return 'UTC';\n  }\n}\n\n/**\n * Convert a time from one timezone to another\n */\nexport function convertTimeZone(\n  date: Date,\n  fromTimeZone: string,\n  toTimeZone: string\n): Date {\n  try {\n    // Convert to UTC first, then to target timezone\n    const utcTime = zonedTimeToUtc(date, fromTimeZone);\n    return utcToZonedTime(utcTime, toTimeZone);\n  } catch (error) {\n    console.error('Error converting timezone:', error);\n    return date;\n  }\n}\n\n/**\n * Format time according to the specified format\n */\nexport function formatTime(date: Date, format12h: boolean = false): string {\n  try {\n    if (format12h) {\n      return format(date, 'h:mm:ss a');\n    } else {\n      return format(date, 'HH:mm:ss');\n    }\n  } catch (error) {\n    console.error('Error formatting time:', error);\n    return '00:00:00';\n  }\n}\n\n/**\n * Format date in a readable format\n */\nexport function formatDate(date: Date): string {\n  try {\n    return format(date, 'EEEE, MMMM d, yyyy');\n  } catch (error) {\n    console.error('Error formatting date:', error);\n    return '';\n  }\n}\n\n/**\n * Get timezone abbreviation for a given timezone and date\n */\nexport function getTimezoneAbbreviation(timeZone: string, date: Date = new Date()): string {\n  try {\n    const formatter = new Intl.DateTimeFormat('en', {\n      timeZone,\n      timeZoneName: 'short'\n    });\n    \n    const parts = formatter.formatToParts(date);\n    const timeZonePart = parts.find(part => part.type === 'timeZoneName');\n    \n    return timeZonePart?.value || timeZone.split('/').pop()?.replace('_', ' ') || timeZone;\n  } catch (error) {\n    console.error('Error getting timezone abbreviation:', error);\n    return timeZone.split('/').pop()?.replace('_', ' ') || timeZone;\n  }\n}\n\n/**\n * Parse time string in various formats\n */\nexport function parseTimeString(timeString: string, is12Hour: boolean = false): { hours: number; minutes: number } | null {\n  try {\n    if (is12Hour) {\n      // Handle 12-hour format (e.g., \"2:30 PM\", \"2:30PM\", \"2:30 pm\")\n      const match = timeString.match(/^(\\d{1,2}):(\\d{2})\\s*(AM|PM|am|pm)$/i);\n      if (!match) return null;\n      \n      let hours = parseInt(match[1], 10);\n      const minutes = parseInt(match[2], 10);\n      const period = match[3].toUpperCase();\n      \n      if (period === 'PM' && hours !== 12) {\n        hours += 12;\n      } else if (period === 'AM' && hours === 12) {\n        hours = 0;\n      }\n      \n      return { hours, minutes };\n    } else {\n      // Handle 24-hour format (e.g., \"14:30\")\n      const match = timeString.match(/^(\\d{1,2}):(\\d{2})$/);\n      if (!match) return null;\n      \n      const hours = parseInt(match[1], 10);\n      const minutes = parseInt(match[2], 10);\n      \n      if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {\n        return null;\n      }\n      \n      return { hours, minutes };\n    }\n  } catch (error) {\n    console.error('Error parsing time string:', error);\n    return null;\n  }\n}\n\n/**\n * Create a new date with specific time in a timezone\n */\nexport function createTimeInTimezone(\n  baseDate: Date,\n  hours: number,\n  minutes: number,\n  timeZone: string\n): Date {\n  try {\n    const newDate = new Date(baseDate);\n    newDate.setHours(hours, minutes, 0, 0);\n    \n    // Convert to UTC for consistent handling\n    return zonedTimeToUtc(newDate, timeZone);\n  } catch (error) {\n    console.error('Error creating time in timezone:', error);\n    return baseDate;\n  }\n}\n\n/**\n * Check if a timezone is valid\n */\nexport function isValidTimezone(timeZone: string): boolean {\n  try {\n    Intl.DateTimeFormat(undefined, { timeZone });\n    return true;\n  } catch (error) {\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AAAA;;;AAKO,SAAS;IACd,IAAI;QACF,OAAO,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ;IACzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAKO,SAAS,gBACd,IAAU,EACV,YAAoB,EACpB,UAAkB;IAElB,IAAI;QACF,gDAAgD;QAChD,MAAM,UAAU,CAAA,GAAA,6KAAA,CAAA,iBAAc,AAAD,EAAE,MAAM;QACrC,OAAO,CAAA,GAAA,6KAAA,CAAA,iBAAc,AAAD,EAAE,SAAS;IACjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAKO,SAAS,WAAW,IAAU,EAAE,YAAqB,KAAK;IAC/D,IAAI;QACF,IAAI,WAAW;YACb,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;QACtB,OAAO;YACL,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;QACtB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;IACT;AACF;AAKO,SAAS,WAAW,IAAU;IACnC,IAAI;QACF,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;IACT;AACF;AAKO,SAAS,wBAAwB,QAAgB,EAAE,OAAa,IAAI,MAAM;IAC/E,IAAI;QACF,MAAM,YAAY,IAAI,KAAK,cAAc,CAAC,MAAM;YAC9C;YACA,cAAc;QAChB;QAEA,MAAM,QAAQ,UAAU,aAAa,CAAC;QACtC,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;QAEtD,OAAO,cAAc,SAAS,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI,QAAQ,KAAK,QAAQ;IAChF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI,QAAQ,KAAK,QAAQ;IACzD;AACF;AAKO,SAAS,gBAAgB,UAAkB,EAAE,WAAoB,KAAK;IAC3E,IAAI;QACF,IAAI,UAAU;YACZ,+DAA+D;YAC/D,MAAM,QAAQ,WAAW,KAAK,CAAC;YAC/B,IAAI,CAAC,OAAO,OAAO;YAEnB,IAAI,QAAQ,SAAS,KAAK,CAAC,EAAE,EAAE;YAC/B,MAAM,UAAU,SAAS,KAAK,CAAC,EAAE,EAAE;YACnC,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC,WAAW;YAEnC,IAAI,WAAW,QAAQ,UAAU,IAAI;gBACnC,SAAS;YACX,OAAO,IAAI,WAAW,QAAQ,UAAU,IAAI;gBAC1C,QAAQ;YACV;YAEA,OAAO;gBAAE;gBAAO;YAAQ;QAC1B,OAAO;YACL,wCAAwC;YACxC,MAAM,QAAQ,WAAW,KAAK,CAAC;YAC/B,IAAI,CAAC,OAAO,OAAO;YAEnB,MAAM,QAAQ,SAAS,KAAK,CAAC,EAAE,EAAE;YACjC,MAAM,UAAU,SAAS,KAAK,CAAC,EAAE,EAAE;YAEnC,IAAI,QAAQ,KAAK,QAAQ,MAAM,UAAU,KAAK,UAAU,IAAI;gBAC1D,OAAO;YACT;YAEA,OAAO;gBAAE;gBAAO;YAAQ;QAC1B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAKO,SAAS,qBACd,QAAc,EACd,KAAa,EACb,OAAe,EACf,QAAgB;IAEhB,IAAI;QACF,MAAM,UAAU,IAAI,KAAK;QACzB,QAAQ,QAAQ,CAAC,OAAO,SAAS,GAAG;QAEpC,yCAAyC;QACzC,OAAO,CAAA,GAAA,6KAAA,CAAA,iBAAc,AAAD,EAAE,SAAS;IACjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAKO,SAAS,gBAAgB,QAAgB;IAC9C,IAAI;QACF,KAAK,cAAc,CAAC,WAAW;YAAE;QAAS;QAC1C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1544, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Header from './components/Header';\nimport CitySearchInput from './components/CitySearchInput';\nimport TimeZoneList from './components/TimeZoneList';\nimport { useTheme, useTimeFormat, useTimeZones } from './hooks/useLocalStorage';\nimport { getUserTimezone } from './utils/time';\nimport { getTimezoneByIdentifier } from './utils/timezones';\n\nexport default function Home() {\n  const [theme, setTheme] = useTheme();\n  const [timeFormat, setTimeFormat] = useTimeFormat();\n  const { timeZones, addTimeZone, removeTimeZone } = useTimeZones();\n  const [referenceTime, setReferenceTime] = useState<Date | null>(null);\n\n  // Initialize with user's local timezone on first load\n  useEffect(() => {\n    if (timeZones.length === 0) {\n      const userTimezone = getUserTimezone();\n      const localCity = getTimezoneByIdentifier(userTimezone);\n\n      if (localCity) {\n        addTimeZone({\n          ...localCity,\n          id: `local-${Date.now()}`\n        });\n      } else {\n        // Fallback: add a generic local timezone entry\n        addTimeZone({\n          city: 'Local Time',\n          timeZone: userTimezone,\n          id: `local-${Date.now()}`\n        });\n      }\n    }\n  }, [timeZones.length, addTimeZone]);\n\n  const handleCitySelect = (city: { city: string; timeZone: string; id: string }) => {\n    addTimeZone(city);\n  };\n\n  const handleTimeChange = (newTime: Date) => {\n    setReferenceTime(newTime);\n  };\n\n  const handleRemoveTimeZone = (id: string) => {\n    removeTimeZone(id);\n    // Reset reference time when removing a timezone\n    if (referenceTime) {\n      setReferenceTime(null);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Header\n        timeFormat={timeFormat}\n        onTimeFormatChange={setTimeFormat}\n        theme={theme}\n        onThemeChange={setTheme}\n      />\n\n      <main className=\"max-w-6xl mx-auto px-4 py-8\">\n        <div className=\"space-y-8\">\n          {/* Search Input */}\n          <div className=\"flex justify-center\">\n            <CitySearchInput onCitySelect={handleCitySelect} />\n          </div>\n\n          {/* Time Zone Cards */}\n          <TimeZoneList\n            timeZones={timeZones}\n            timeFormat={timeFormat}\n            referenceTime={referenceTime}\n            onTimeChange={handleTimeChange}\n            onRemoveTimeZone={handleRemoveTimeZone}\n          />\n        </div>\n      </main>\n\n      <footer className=\"mt-16 py-8 border-t border-border\">\n        <div className=\"max-w-6xl mx-auto px-4 text-center\">\n          <p className=\"text-muted-foreground text-sm\">\n            Built with ❤️ by{' '}\n            <a\n              href=\"https://github.com/chirag127\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-primary hover:underline\"\n            >\n              Chirag Singhal\n            </a>\n            {' '}• Open source on{' '}\n            <a\n              href=\"https://github.com/chirag127/Time-Zone-Converter\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-primary hover:underline\"\n            >\n              GitHub\n            </a>\n          </p>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IAChD,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,eAAY,AAAD;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAEhE,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,UAAU,MAAM,KAAK,GAAG;gBAC1B,MAAM,eAAe,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD;gBACnC,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,0BAAuB,AAAD,EAAE;gBAE1C,IAAI,WAAW;oBACb,YAAY;wBACV,GAAG,SAAS;wBACZ,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;oBAC3B;gBACF,OAAO;oBACL,+CAA+C;oBAC/C,YAAY;wBACV,MAAM;wBACN,UAAU;wBACV,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;oBAC3B;gBACF;YACF;QACF;yBAAG;QAAC,UAAU,MAAM;QAAE;KAAY;IAElC,MAAM,mBAAmB,CAAC;QACxB,YAAY;IACd;IAEA,MAAM,mBAAmB,CAAC;QACxB,iBAAiB;IACnB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,eAAe;QACf,gDAAgD;QAChD,IAAI,eAAe;YACjB,iBAAiB;QACnB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,sIAAA,CAAA,UAAM;gBACL,YAAY;gBACZ,oBAAoB;gBACpB,OAAO;gBACP,eAAe;;;;;;0BAGjB,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+IAAA,CAAA,UAAe;gCAAC,cAAc;;;;;;;;;;;sCAIjC,6LAAC,4IAAA,CAAA,UAAY;4BACX,WAAW;4BACX,YAAY;4BACZ,eAAe;4BACf,cAAc;4BACd,kBAAkB;;;;;;;;;;;;;;;;;0BAKxB,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAAgC;4BAC1B;0CACjB,6LAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;0CACX;;;;;;4BAGA;4BAAI;4BAAiB;0CACtB,6LAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAjGwB;;QACI,yIAAA,CAAA,WAAQ;QACE,yIAAA,CAAA,gBAAa;QACE,yIAAA,CAAA,eAAY;;;KAHzC", "debugId": null}}]}