'use client';

import React from 'react';
import TimeZoneCard from './TimeZoneCard';

interface TimeZoneSelection {
  city: string;
  timeZone: string;
  id: string;
}

interface TimeZoneListProps {
  timeZones: TimeZoneSelection[];
  timeFormat: '12h' | '24h';
  referenceTime: Date | null;
  onTimeChange: (newTime: Date) => void;
  onRemoveTimeZone: (id: string) => void;
  showTimeDifference?: boolean;
  userTimeZone?: string;
}

export default function TimeZoneList({
  timeZones,
  timeFormat,
  referenceTime,
  onTimeChange,
  onRemoveTimeZone,
  showTimeDifference = false,
  userTimeZone
}: TimeZoneListProps) {
  if (timeZones.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="max-w-md mx-auto">
          <svg
            className="w-16 h-16 mx-auto text-muted-foreground mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <h3 className="text-lg font-medium text-foreground mb-2">
            No time zones added yet
          </h3>
          <p className="text-muted-foreground">
            Search for a city above to add your first time zone and start converting times.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {timeZones.map((tz) => (
        <TimeZoneCard
          key={tz.id}
          city={tz.city}
          timeZone={tz.timeZone}
          id={tz.id}
          timeFormat={timeFormat}
          referenceTime={referenceTime}
          onTimeChange={onTimeChange}
          onRemove={onRemoveTimeZone}
          showTimeDifference={showTimeDifference}
          userTimeZone={userTimeZone}
        />
      ))}
    </div>
  );
}
