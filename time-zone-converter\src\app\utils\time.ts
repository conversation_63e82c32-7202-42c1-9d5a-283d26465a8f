import { format, parseISO } from 'date-fns';
import { zonedTimeToUtc, utcToZonedTime } from 'date-fns-tz';

/**
 * Get the user's local timezone
 */
export function getUserTimezone(): string {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (error) {
    console.error('Error getting user timezone:', error);
    return 'UTC';
  }
}

/**
 * Convert a time from one timezone to another
 */
export function convertTimeZone(
  date: Date,
  fromTimeZone: string,
  toTimeZone: string
): Date {
  try {
    // Convert to UTC first, then to target timezone
    const utcTime = zonedTimeToUtc(date, fromTimeZone);
    return utcToZonedTime(utcTime, toTimeZone);
  } catch (error) {
    console.error('Error converting timezone:', error);
    return date;
  }
}

/**
 * Format time according to the specified format
 */
export function formatTime(date: Date, format12h: boolean = false): string {
  try {
    if (format12h) {
      return format(date, 'h:mm:ss a');
    } else {
      return format(date, 'HH:mm:ss');
    }
  } catch (error) {
    console.error('Error formatting time:', error);
    return '00:00:00';
  }
}

/**
 * Format date in a readable format
 */
export function formatDate(date: Date): string {
  try {
    return format(date, 'EEEE, MMMM d, yyyy');
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
}

/**
 * Get timezone abbreviation for a given timezone and date
 */
export function getTimezoneAbbreviation(timeZone: string, date: Date = new Date()): string {
  try {
    const formatter = new Intl.DateTimeFormat('en', {
      timeZone,
      timeZoneName: 'short'
    });
    
    const parts = formatter.formatToParts(date);
    const timeZonePart = parts.find(part => part.type === 'timeZoneName');
    
    return timeZonePart?.value || timeZone.split('/').pop()?.replace('_', ' ') || timeZone;
  } catch (error) {
    console.error('Error getting timezone abbreviation:', error);
    return timeZone.split('/').pop()?.replace('_', ' ') || timeZone;
  }
}

/**
 * Parse time string in various formats
 */
export function parseTimeString(timeString: string, is12Hour: boolean = false): { hours: number; minutes: number } | null {
  try {
    if (is12Hour) {
      // Handle 12-hour format (e.g., "2:30 PM", "2:30PM", "2:30 pm")
      const match = timeString.match(/^(\d{1,2}):(\d{2})\s*(AM|PM|am|pm)$/i);
      if (!match) return null;
      
      let hours = parseInt(match[1], 10);
      const minutes = parseInt(match[2], 10);
      const period = match[3].toUpperCase();
      
      if (period === 'PM' && hours !== 12) {
        hours += 12;
      } else if (period === 'AM' && hours === 12) {
        hours = 0;
      }
      
      return { hours, minutes };
    } else {
      // Handle 24-hour format (e.g., "14:30")
      const match = timeString.match(/^(\d{1,2}):(\d{2})$/);
      if (!match) return null;
      
      const hours = parseInt(match[1], 10);
      const minutes = parseInt(match[2], 10);
      
      if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
        return null;
      }
      
      return { hours, minutes };
    }
  } catch (error) {
    console.error('Error parsing time string:', error);
    return null;
  }
}

/**
 * Create a new date with specific time in a timezone
 */
export function createTimeInTimezone(
  baseDate: Date,
  hours: number,
  minutes: number,
  timeZone: string
): Date {
  try {
    const newDate = new Date(baseDate);
    newDate.setHours(hours, minutes, 0, 0);
    
    // Convert to UTC for consistent handling
    return zonedTimeToUtc(newDate, timeZone);
  } catch (error) {
    console.error('Error creating time in timezone:', error);
    return baseDate;
  }
}

/**
 * Check if a timezone is valid
 */
export function isValidTimezone(timeZone: string): boolean {
  try {
    Intl.DateTimeFormat(undefined, { timeZone });
    return true;
  } catch (error) {
    return false;
  }
}
