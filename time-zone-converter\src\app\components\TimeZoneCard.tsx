'use client';

import React, { useState, useEffect, useRef } from 'react';
import { format, parseISO } from 'date-fns';
import { zonedTimeToUtc, utcToZonedTime } from 'date-fns-tz';

interface TimeZoneCardProps {
  city: string;
  timeZone: string;
  id: string;
  timeFormat: '12h' | '24h';
  referenceTime: Date | null;
  onTimeChange: (newTime: Date) => void;
  onRemove: (id: string) => void;
}

export default function TimeZoneCard({
  city,
  timeZone,
  id,
  timeFormat,
  referenceTime,
  onTimeChange,
  onRemove
}: TimeZoneCardProps) {
  const [currentTime, setCurrentTime] = useState<Date>(new Date());
  const [isEditing, setIsEditing] = useState(false);
  const [editTime, setEditTime] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  // Update current time every second or when reference time changes
  useEffect(() => {
    const updateTime = () => {
      if (referenceTime) {
        // Use reference time to calculate this timezone's time
        const zonedTime = utcToZonedTime(referenceTime, timeZone);
        setCurrentTime(zonedTime);
      } else {
        // Use current time
        const now = new Date();
        const zonedTime = utcToZonedTime(now, timeZone);
        setCurrentTime(zonedTime);
      }
    };

    updateTime();
    const interval = setInterval(updateTime, 1000);
    return () => clearInterval(interval);
  }, [timeZone, referenceTime]);

  // Format time based on selected format
  const formatTime = (date: Date) => {
    try {
      if (timeFormat === '12h') {
        return format(date, 'h:mm:ss a');
      } else {
        return format(date, 'HH:mm:ss');
      }
    } catch (error) {
      console.error('Error formatting time:', error);
      return '00:00:00';
    }
  };

  // Format date
  const formatDate = (date: Date) => {
    try {
      return format(date, 'EEEE, MMMM d, yyyy');
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  // Get timezone abbreviation
  const getTimezoneAbbr = () => {
    try {
      return new Intl.DateTimeFormat('en', {
        timeZone,
        timeZoneName: 'short'
      }).formatToParts(currentTime)
        .find(part => part.type === 'timeZoneName')?.value || timeZone.split('/').pop();
    } catch (error) {
      return timeZone.split('/').pop();
    }
  };

  const handleTimeClick = () => {
    setIsEditing(true);
    setEditTime(format(currentTime, timeFormat === '12h' ? 'h:mm a' : 'HH:mm'));
  };

  const handleTimeSubmit = () => {
    try {
      let newTime: Date;
      
      if (timeFormat === '12h') {
        // Parse 12-hour format (e.g., "2:30 PM")
        const [time, period] = editTime.split(' ');
        const [hours, minutes] = time.split(':').map(Number);
        
        let hour24 = hours;
        if (period?.toLowerCase() === 'pm' && hours !== 12) {
          hour24 += 12;
        } else if (period?.toLowerCase() === 'am' && hours === 12) {
          hour24 = 0;
        }
        
        newTime = new Date(currentTime);
        newTime.setHours(hour24, minutes, 0, 0);
      } else {
        // Parse 24-hour format (e.g., "14:30")
        const [hours, minutes] = editTime.split(':').map(Number);
        newTime = new Date(currentTime);
        newTime.setHours(hours, minutes, 0, 0);
      }

      // Convert to UTC for reference
      const utcTime = zonedTimeToUtc(newTime, timeZone);
      onTimeChange(utcTime);
      
      setIsEditing(false);
    } catch (error) {
      console.error('Error parsing time:', error);
      setIsEditing(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleTimeSubmit();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
    }
  };

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  return (
    <div className="bg-card border border-border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-3">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-foreground">{city}</h3>
          <p className="text-sm text-muted-foreground">{getTimezoneAbbr()}</p>
        </div>
        <button
          onClick={() => onRemove(id)}
          className="p-1 text-muted-foreground hover:text-destructive transition-colors"
          aria-label={`Remove ${city}`}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div className="space-y-2">
        <p className="text-sm text-muted-foreground">{formatDate(currentTime)}</p>
        
        {isEditing ? (
          <input
            ref={inputRef}
            type="text"
            value={editTime}
            onChange={(e) => setEditTime(e.target.value)}
            onBlur={handleTimeSubmit}
            onKeyDown={handleKeyDown}
            className="text-2xl font-mono font-bold bg-input border border-border rounded px-2 py-1 w-full focus:outline-none focus:ring-2 focus:ring-ring"
            placeholder={timeFormat === '12h' ? '12:00 PM' : '12:00'}
          />
        ) : (
          <button
            onClick={handleTimeClick}
            className="text-2xl font-mono font-bold text-foreground hover:text-primary transition-colors cursor-pointer"
            aria-label={`Edit time for ${city}. Current time: ${formatTime(currentTime)}`}
          >
            {formatTime(currentTime)}
          </button>
        )}
      </div>
    </div>
  );
}
