'use client';

import React from 'react';

interface HeaderProps {
  timeFormat: '12h' | '24h';
  onTimeFormatChange: (format: '12h' | '24h') => void;
  theme: 'light' | 'dark';
  onThemeChange: (theme: 'light' | 'dark') => void;
}

export default function Header({ 
  timeFormat, 
  onTimeFormatChange, 
  theme, 
  onThemeChange 
}: HeaderProps) {
  return (
    <header className="bg-card border-b border-border p-4">
      <div className="max-w-4xl mx-auto flex flex-col sm:flex-row justify-between items-center gap-4">
        <div className="text-center sm:text-left">
          <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
            Time Zone Converter
          </h1>
          <p className="text-muted-foreground text-sm sm:text-base">
            Coordinate across time zones effortlessly
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          {/* Time Format Toggle */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Time:</span>
            <button
              onClick={() => onTimeFormatChange(timeFormat === '12h' ? '24h' : '12h')}
              className="flex items-center bg-secondary rounded-lg p-1 transition-colors"
              aria-label={`Switch to ${timeFormat === '12h' ? '24-hour' : '12-hour'} format`}
            >
              <span
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  timeFormat === '12h'
                    ? 'bg-primary text-primary-foreground'
                    : 'text-secondary-foreground hover:bg-accent'
                }`}
              >
                12h
              </span>
              <span
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  timeFormat === '24h'
                    ? 'bg-primary text-primary-foreground'
                    : 'text-secondary-foreground hover:bg-accent'
                }`}
              >
                24h
              </span>
            </button>
          </div>

          {/* Theme Toggle */}
          <button
            onClick={() => onThemeChange(theme === 'light' ? 'dark' : 'light')}
            className="p-2 rounded-lg bg-secondary hover:bg-accent transition-colors"
            aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
          >
            {theme === 'light' ? (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
              </svg>
            ) : (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            )}
          </button>
        </div>
      </div>
    </header>
  );
}
