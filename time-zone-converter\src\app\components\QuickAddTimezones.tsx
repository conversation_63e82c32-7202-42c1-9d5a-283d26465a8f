'use client';

import React from 'react';

interface QuickAddTimezonesProps {
  onCitySelect: (city: { city: string; timeZone: string; id: string }) => void;
  existingTimezones: string[];
}

const popularTimezones = [
  { city: 'New York', timeZone: 'America/New_York' },
  { city: 'Los Angeles', timeZone: 'America/Los_Angeles' },
  { city: 'London', timeZone: 'Europe/London' },
  { city: 'Paris', timeZone: 'Europe/Paris' },
  { city: 'Tokyo', timeZone: 'Asia/Tokyo' },
  { city: 'Sydney', timeZone: 'Australia/Sydney' },
  { city: 'Dubai', timeZone: 'Asia/Dubai' },
  { city: 'Singapore', timeZone: 'Asia/Singapore' },
  { city: 'Hong Kong', timeZone: 'Asia/Hong_Kong' },
  { city: 'Mumbai', timeZone: 'Asia/Kolkata' },
  { city: 'São Paulo', timeZone: 'America/Sao_Paulo' },
  { city: 'Mexico City', timeZone: 'America/Mexico_City' }
];

export default function QuickAddTimezones({ onCitySelect, existingTimezones }: QuickAddTimezonesProps) {
  const availableTimezones = popularTimezones.filter(
    tz => !existingTimezones.includes(tz.timeZone)
  );

  if (availableTimezones.length === 0) {
    return null;
  }

  const handleQuickAdd = (timezone: typeof popularTimezones[0]) => {
    onCitySelect({
      ...timezone,
      id: `quick-${timezone.timeZone}-${Date.now()}`
    });
  };

  return (
    <div className="bg-card border border-border rounded-lg p-4">
      <h3 className="text-sm font-medium text-foreground mb-3">Quick Add Popular Cities</h3>
      <div className="flex flex-wrap gap-2">
        {availableTimezones.slice(0, 8).map((timezone) => (
          <button
            key={timezone.timeZone}
            onClick={() => handleQuickAdd(timezone)}
            className="px-3 py-1.5 text-sm bg-secondary text-secondary-foreground rounded-md hover:bg-accent hover:text-accent-foreground transition-colors"
          >
            {timezone.city}
          </button>
        ))}
      </div>
    </div>
  );
}
