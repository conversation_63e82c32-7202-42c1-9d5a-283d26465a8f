{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/shared/lib/router/utils/handle-smooth-scroll.ts"], "sourcesContent": ["/**\n * Run function with `scroll-behavior: auto` applied to `<html/>`.\n * This css change will be reverted after the function finishes.\n */\nexport function handleSmoothScroll(\n  fn: () => void,\n  options: { dontForceLayout?: boolean; onlyHashChange?: boolean } = {}\n) {\n  // if only the hash is changed, we don't need to disable smooth scrolling\n  // we only care to prevent smooth scrolling when navigating to a new page to avoid jarring UX\n  if (options.onlyHashChange) {\n    fn()\n    return\n  }\n  const htmlElement = document.documentElement\n  const existing = htmlElement.style.scrollBehavior\n  htmlElement.style.scrollBehavior = 'auto'\n  if (!options.dontForceLayout) {\n    // In Chrome-based browsers we need to force reflow before calling `scrollTo`.\n    // Otherwise it will not pickup the change in scrollBehavior\n    // More info here: https://github.com/vercel/next.js/issues/40719#issuecomment-1336248042\n    htmlElement.getClientRects()\n  }\n  fn()\n  htmlElement.style.scrollBehavior = existing\n}\n"], "names": ["handleSmoothScroll", "fn", "options", "onlyHashChange", "htmlElement", "document", "documentElement", "existing", "style", "scroll<PERSON>eh<PERSON>or", "dontForceLayout", "getClientRects"], "mappings": "AAAA;;;CAGC,GAAA;;;;+BACeA,sBAAAA;;;eAAAA;;;AAAT,SAASA,mBACdC,EAAc,EACdC,OAAqE;IAArEA,IAAAA,YAAAA,KAAAA,GAAAA,UAAmE,CAAC;IAEpE,yEAAyE;IACzE,6FAA6F;IAC7F,IAAIA,QAAQC,cAAc,EAAE;QAC1BF;QACA;IACF;IACA,MAAMG,cAAcC,SAASC,eAAe;IAC5C,MAAMC,WAAWH,YAAYI,KAAK,CAACC,cAAc;IACjDL,YAAYI,KAAK,CAACC,cAAc,GAAG;IACnC,IAAI,CAACP,QAAQQ,eAAe,EAAE;QAC5B,8EAA8E;QAC9E,4DAA4D;QAC5D,yFAAyF;QACzFN,YAAYO,cAAc;IAC5B;IACAV;IACAG,YAAYI,KAAK,CAACC,cAAc,GAAGF;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/client/components/layout-router.tsx"], "sourcesContent": ["'use client'\n\nimport type {\n  <PERSON>ache<PERSON>ode,\n  LazyCacheNode,\n  LoadingModuleData,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../server/app-render/types'\nimport type { ErrorComponent } from './error-boundary'\nimport {\n  ACTION_SERVER_PATCH,\n  type FocusAndScrollRef,\n} from './router-reducer/router-reducer-types'\n\nimport React, {\n  useContext,\n  use,\n  startTransition,\n  Suspense,\n  useDeferredValue,\n  type JSX,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport {\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n  TemplateContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { fetchServerResponse } from './router-reducer/fetch-server-response'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { ErrorBoundary } from './error-boundary'\nimport { matchSegment } from './match-segments'\nimport { handleSmoothScroll } from '../../shared/lib/router/utils/handle-smooth-scroll'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary'\nimport { createRouterCacheKey } from './router-reducer/create-router-cache-key'\nimport { hasInterceptionRouteInCurrentTree } from './router-reducer/reducers/has-interception-route-in-current-tree'\nimport { dispatchAppRouterAction } from './use-action-queue'\n\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */\nfunction walkAddRefetch(\n  segmentPathToWalk: FlightSegmentPath | undefined,\n  treeToRecreate: FlightRouterState\n): FlightRouterState {\n  if (segmentPathToWalk) {\n    const [segment, parallelRouteKey] = segmentPathToWalk\n    const isLast = segmentPathToWalk.length === 2\n\n    if (matchSegment(treeToRecreate[0], segment)) {\n      if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n        if (isLast) {\n          const subTree = walkAddRefetch(\n            undefined,\n            treeToRecreate[1][parallelRouteKey]\n          )\n          return [\n            treeToRecreate[0],\n            {\n              ...treeToRecreate[1],\n              [parallelRouteKey]: [\n                subTree[0],\n                subTree[1],\n                subTree[2],\n                'refetch',\n              ],\n            },\n          ]\n        }\n\n        return [\n          treeToRecreate[0],\n          {\n            ...treeToRecreate[1],\n            [parallelRouteKey]: walkAddRefetch(\n              segmentPathToWalk.slice(2),\n              treeToRecreate[1][parallelRouteKey]\n            ),\n          },\n        ]\n      }\n    }\n  }\n\n  return treeToRecreate\n}\n\nconst __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = (\n  ReactDOM as any\n).__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE\n\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */\nfunction findDOMNode(\n  instance: React.ReactInstance | null | undefined\n): Element | Text | null {\n  // Tree-shake for server bundle\n  if (typeof window === 'undefined') return null\n\n  // __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode is null during module init.\n  // We need to lazily reference it.\n  const internal_reactDOMfindDOMNode =\n    __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode\n  return internal_reactDOMfindDOMNode(instance)\n}\n\nconst rectProperties = [\n  'bottom',\n  'height',\n  'left',\n  'right',\n  'top',\n  'width',\n  'x',\n  'y',\n] as const\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */\nfunction shouldSkipElement(element: HTMLElement) {\n  // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n  // and will result in a situation we bail on scroll because of something like a fixed nav,\n  // even though the actual page content is offscreen\n  if (['sticky', 'fixed'].includes(getComputedStyle(element).position)) {\n    if (process.env.NODE_ENV === 'development') {\n      console.warn(\n        'Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:',\n        element\n      )\n    }\n    return true\n  }\n\n  // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n  // because `offsetParent` doesn't consider document/body\n  const rect = element.getBoundingClientRect()\n  return rectProperties.every((item) => rect[item] === 0)\n}\n\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */\nfunction topOfElementInViewport(element: HTMLElement, viewportHeight: number) {\n  const rect = element.getBoundingClientRect()\n  return rect.top >= 0 && rect.top <= viewportHeight\n}\n\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */\nfunction getHashFragmentDomNode(hashFragment: string) {\n  // If the hash fragment is `top` the page has to scroll to the top of the page.\n  if (hashFragment === 'top') {\n    return document.body\n  }\n\n  // If the hash fragment is an id, the page has to scroll to the element with that id.\n  return (\n    document.getElementById(hashFragment) ??\n    // If the hash fragment is a name, the page has to scroll to the first element with that name.\n    document.getElementsByName(hashFragment)[0]\n  )\n}\ninterface ScrollAndFocusHandlerProps {\n  focusAndScrollRef: FocusAndScrollRef\n  children: React.ReactNode\n  segmentPath: FlightSegmentPath\n}\nclass InnerScrollAndFocusHandler extends React.Component<ScrollAndFocusHandlerProps> {\n  handlePotentialScroll = () => {\n    // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n    const { focusAndScrollRef, segmentPath } = this.props\n\n    if (focusAndScrollRef.apply) {\n      // segmentPaths is an array of segment paths that should be scrolled to\n      // if the current segment path is not in the array, the scroll is not applied\n      // unless the array is empty, in which case the scroll is always applied\n      if (\n        focusAndScrollRef.segmentPaths.length !== 0 &&\n        !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath) =>\n          segmentPath.every((segment, index) =>\n            matchSegment(segment, scrollRefSegmentPath[index])\n          )\n        )\n      ) {\n        return\n      }\n\n      let domNode:\n        | ReturnType<typeof getHashFragmentDomNode>\n        | ReturnType<typeof findDOMNode> = null\n      const hashFragment = focusAndScrollRef.hashFragment\n\n      if (hashFragment) {\n        domNode = getHashFragmentDomNode(hashFragment)\n      }\n\n      // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n      // This already caused a bug where the first child was a <link/> in head.\n      if (!domNode) {\n        domNode = findDOMNode(this)\n      }\n\n      // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n      if (!(domNode instanceof Element)) {\n        return\n      }\n\n      // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n      // If the element is skipped, try to select the next sibling and try again.\n      while (!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)) {\n        if (process.env.NODE_ENV !== 'production') {\n          if (domNode.parentElement?.localName === 'head') {\n            // TODO: We enter this state when metadata was rendered as part of the page or via Next.js.\n            // This is always a bug in Next.js and caused by React hoisting metadata.\n            // We need to replace `findDOMNode` in favor of Fragment Refs (when available) so that we can skip over metadata.\n          }\n        }\n\n        // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n        if (domNode.nextElementSibling === null) {\n          return\n        }\n        domNode = domNode.nextElementSibling\n      }\n\n      // State is mutated to ensure that the focus and scroll is applied only once.\n      focusAndScrollRef.apply = false\n      focusAndScrollRef.hashFragment = null\n      focusAndScrollRef.segmentPaths = []\n\n      handleSmoothScroll(\n        () => {\n          // In case of hash scroll, we only need to scroll the element into view\n          if (hashFragment) {\n            ;(domNode as HTMLElement).scrollIntoView()\n\n            return\n          }\n          // Store the current viewport height because reading `clientHeight` causes a reflow,\n          // and it won't change during this function.\n          const htmlElement = document.documentElement\n          const viewportHeight = htmlElement.clientHeight\n\n          // If the element's top edge is already in the viewport, exit early.\n          if (topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            return\n          }\n\n          // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n          // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n          // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n          // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n          htmlElement.scrollTop = 0\n\n          // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n          if (!topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            // Scroll into view doesn't scroll horizontally by default when not needed\n            ;(domNode as HTMLElement).scrollIntoView()\n          }\n        },\n        {\n          // We will force layout by querying domNode position\n          dontForceLayout: true,\n          onlyHashChange: focusAndScrollRef.onlyHashChange,\n        }\n      )\n\n      // Mutate after scrolling so that it can be read by `handleSmoothScroll`\n      focusAndScrollRef.onlyHashChange = false\n\n      // Set focus on the element\n      domNode.focus()\n    }\n  }\n\n  componentDidMount() {\n    this.handlePotentialScroll()\n  }\n\n  componentDidUpdate() {\n    // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n    if (this.props.focusAndScrollRef.apply) {\n      this.handlePotentialScroll()\n    }\n  }\n\n  render() {\n    return this.props.children\n  }\n}\n\nfunction ScrollAndFocusHandler({\n  segmentPath,\n  children,\n}: {\n  segmentPath: FlightSegmentPath\n  children: React.ReactNode\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  return (\n    <InnerScrollAndFocusHandler\n      segmentPath={segmentPath}\n      focusAndScrollRef={context.focusAndScrollRef}\n    >\n      {children}\n    </InnerScrollAndFocusHandler>\n  )\n}\n\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */\nfunction InnerLayoutRouter({\n  tree,\n  segmentPath,\n  cacheNode,\n  url,\n}: {\n  tree: FlightRouterState\n  segmentPath: FlightSegmentPath\n  cacheNode: CacheNode\n  url: string\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  const { tree: fullTree } = context\n\n  // `rsc` represents the renderable node for this segment.\n\n  // If this segment has a `prefetchRsc`, it's the statically prefetched data.\n  // We should use that on initial render instead of `rsc`. Then we'll switch\n  // to `rsc` when the dynamic response streams in.\n  //\n  // If no prefetch data is available, then we go straight to rendering `rsc`.\n  const resolvedPrefetchRsc =\n    cacheNode.prefetchRsc !== null ? cacheNode.prefetchRsc : cacheNode.rsc\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  const rsc: any = useDeferredValue(cacheNode.rsc, resolvedPrefetchRsc)\n\n  // `rsc` is either a React node or a promise for a React node, except we\n  // special case `null` to represent that this segment's data is missing. If\n  // it's a promise, we need to unwrap it so we can determine whether or not the\n  // data is missing.\n  const resolvedRsc: React.ReactNode =\n    typeof rsc === 'object' && rsc !== null && typeof rsc.then === 'function'\n      ? use(rsc)\n      : rsc\n\n  if (!resolvedRsc) {\n    // The data for this segment is not available, and there's no pending\n    // navigation that will be able to fulfill it. We need to fetch more from\n    // the server and patch the cache.\n\n    // Check if there's already a pending request.\n    let lazyData = cacheNode.lazyData\n    if (lazyData === null) {\n      /**\n       * Router state with refetch marker added\n       */\n      // TODO-APP: remove ''\n      const refetchTree = walkAddRefetch(['', ...segmentPath], fullTree)\n      const includeNextUrl = hasInterceptionRouteInCurrentTree(fullTree)\n      const navigatedAt = Date.now()\n      cacheNode.lazyData = lazyData = fetchServerResponse(\n        new URL(url, location.origin),\n        {\n          flightRouterState: refetchTree,\n          nextUrl: includeNextUrl ? context.nextUrl : null,\n        }\n      ).then((serverResponse) => {\n        startTransition(() => {\n          dispatchAppRouterAction({\n            type: ACTION_SERVER_PATCH,\n            previousTree: fullTree,\n            serverResponse,\n            navigatedAt,\n          })\n        })\n\n        return serverResponse\n      })\n\n      // Suspend while waiting for lazyData to resolve\n      use(lazyData)\n    }\n    // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n    // A falsey `resolvedRsc` indicates missing data -- we should not commit that branch, and we need to wait for the data to arrive.\n    use(unresolvedThenable) as never\n  }\n\n  // If we get to this point, then we know we have something we can render.\n  const subtree = (\n    // The layout router context narrows down tree and childNodes at each level.\n    <LayoutRouterContext.Provider\n      value={{\n        parentTree: tree,\n        parentCacheNode: cacheNode,\n        parentSegmentPath: segmentPath,\n\n        // TODO-APP: overriding of url for parallel routes\n        url: url,\n      }}\n    >\n      {resolvedRsc}\n    </LayoutRouterContext.Provider>\n  )\n  // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n  return subtree\n}\n\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */\nfunction LoadingBoundary({\n  loading,\n  children,\n}: {\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n  children: React.ReactNode\n}): JSX.Element {\n  // If loading is a promise, unwrap it. This happens in cases where we haven't\n  // yet received the loading data from the server — which includes whether or\n  // not this layout has a loading component at all.\n  //\n  // It's OK to suspend here instead of inside the fallback because this\n  // promise will resolve simultaneously with the data for the segment itself.\n  // So it will never suspend for longer than it would have if we didn't use\n  // a Suspense fallback at all.\n  let loadingModuleData\n  if (\n    typeof loading === 'object' &&\n    loading !== null &&\n    typeof (loading as any).then === 'function'\n  ) {\n    const promiseForLoading = loading as Promise<LoadingModuleData>\n    loadingModuleData = use(promiseForLoading)\n  } else {\n    loadingModuleData = loading as LoadingModuleData\n  }\n\n  if (loadingModuleData) {\n    const loadingRsc = loadingModuleData[0]\n    const loadingStyles = loadingModuleData[1]\n    const loadingScripts = loadingModuleData[2]\n    return (\n      <Suspense\n        fallback={\n          <>\n            {loadingStyles}\n            {loadingScripts}\n            {loadingRsc}\n          </>\n        }\n      >\n        {children}\n      </Suspense>\n    )\n  }\n\n  return <>{children}</>\n}\n\n/**\n * OuterLayoutRouter handles the current segment as well as <Offscreen> rendering of other segments.\n * It can be rendered next to each other with a different `parallelRouterKey`, allowing for Parallel routes.\n */\nexport default function OuterLayoutRouter({\n  parallelRouterKey,\n  error,\n  errorStyles,\n  errorScripts,\n  templateStyles,\n  templateScripts,\n  template,\n  notFound,\n  forbidden,\n  unauthorized,\n}: {\n  parallelRouterKey: string\n  error: ErrorComponent | undefined\n  errorStyles: React.ReactNode | undefined\n  errorScripts: React.ReactNode | undefined\n  templateStyles: React.ReactNode | undefined\n  templateScripts: React.ReactNode | undefined\n  template: React.ReactNode\n  notFound: React.ReactNode | undefined\n  forbidden: React.ReactNode | undefined\n  unauthorized: React.ReactNode | undefined\n}) {\n  const context = useContext(LayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant expected layout router to be mounted')\n  }\n\n  const { parentTree, parentCacheNode, parentSegmentPath, url } = context\n\n  // Get the CacheNode for this segment by reading it from the parent segment's\n  // child map.\n  const parentParallelRoutes = parentCacheNode.parallelRoutes\n  let segmentMap = parentParallelRoutes.get(parallelRouterKey)\n  // If the parallel router cache node does not exist yet, create it.\n  // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n  if (!segmentMap) {\n    segmentMap = new Map()\n    parentParallelRoutes.set(parallelRouterKey, segmentMap)\n  }\n\n  // Get the active segment in the tree\n  // The reason arrays are used in the data format is that these are transferred from the server to the browser so it's optimized to save bytes.\n  const parentTreeSegment = parentTree[0]\n  const tree = parentTree[1][parallelRouterKey]\n  const treeSegment = tree[0]\n\n  const segmentPath =\n    parentSegmentPath === null\n      ? // TODO: The root segment value is currently omitted from the segment\n        // path. This has led to a bunch of special cases scattered throughout\n        // the code. We should clean this up.\n        [parallelRouterKey]\n      : parentSegmentPath.concat([parentTreeSegment, parallelRouterKey])\n\n  // The \"state\" key of a segment is the one passed to React — it represents the\n  // identity of the UI tree. Whenever the state key changes, the tree is\n  // recreated and the state is reset. In the App Router model, search params do\n  // not cause state to be lost, so two segments with the same segment path but\n  // different search params should have the same state key.\n  //\n  // The \"cache\" key of a segment, however, *does* include the search params, if\n  // it's possible that the segment accessed the search params on the server.\n  // (This only applies to page segments; layout segments cannot access search\n  // params on the server.)\n  const cacheKey = createRouterCacheKey(treeSegment)\n  const stateKey = createRouterCacheKey(treeSegment, true) // no search params\n\n  // Read segment path from the parallel router cache node.\n  let cacheNode = segmentMap.get(cacheKey)\n  if (cacheNode === undefined) {\n    // When data is not available during rendering client-side we need to fetch\n    // it from the server.\n    const newLazyCacheNode: LazyCacheNode = {\n      lazyData: null,\n      rsc: null,\n      prefetchRsc: null,\n      head: null,\n      prefetchHead: null,\n      parallelRoutes: new Map(),\n      loading: null,\n      navigatedAt: -1,\n    }\n\n    // Flight data fetch kicked off during render and put into the cache.\n    cacheNode = newLazyCacheNode\n    segmentMap.set(cacheKey, newLazyCacheNode)\n  }\n\n  /*\n    - Error boundary\n      - Only renders error boundary if error component is provided.\n      - Rendered for each segment to ensure they have their own error state.\n    - Loading boundary\n      - Only renders suspense boundary if loading components is provided.\n      - Rendered for each segment to ensure they have their own loading state.\n      - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n  */\n\n  // TODO: The loading module data for a segment is stored on the parent, then\n  // applied to each of that parent segment's parallel route slots. In the\n  // simple case where there's only one parallel route (the `children` slot),\n  // this is no different from if the loading module data where stored on the\n  // child directly. But I'm not sure this actually makes sense when there are\n  // multiple parallel routes. It's not a huge issue because you always have\n  // the option to define a narrower loading boundary for a particular slot. But\n  // this sort of smells like an implementation accident to me.\n  const loadingModuleData = parentCacheNode.loading\n\n  return (\n    <TemplateContext.Provider\n      key={stateKey}\n      value={\n        <ScrollAndFocusHandler segmentPath={segmentPath}>\n          <ErrorBoundary\n            errorComponent={error}\n            errorStyles={errorStyles}\n            errorScripts={errorScripts}\n          >\n            <LoadingBoundary loading={loadingModuleData}>\n              <HTTPAccessFallbackBoundary\n                notFound={notFound}\n                forbidden={forbidden}\n                unauthorized={unauthorized}\n              >\n                <RedirectBoundary>\n                  <InnerLayoutRouter\n                    url={url}\n                    tree={tree}\n                    cacheNode={cacheNode}\n                    segmentPath={segmentPath}\n                  />\n                </RedirectBoundary>\n              </HTTPAccessFallbackBoundary>\n            </LoadingBoundary>\n          </ErrorBoundary>\n        </ScrollAndFocusHandler>\n      }\n    >\n      {templateStyles}\n      {templateScripts}\n      {template}\n    </TemplateContext.Provider>\n  )\n}\n"], "names": ["OuterLayoutRouter", "walkAddRefetch", "segmentPathToWalk", "treeToRecreate", "segment", "parallelRouteKey", "isLast", "length", "matchSegment", "hasOwnProperty", "subTree", "undefined", "slice", "__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "ReactDOM", "findDOMNode", "instance", "window", "internal_reactDOMfindDOMNode", "rectProperties", "shouldSkipElement", "element", "includes", "getComputedStyle", "position", "process", "env", "NODE_ENV", "console", "warn", "rect", "getBoundingClientRect", "every", "item", "topOfElementInViewport", "viewportHeight", "top", "getHashFragmentDomNode", "hashFragment", "document", "body", "getElementById", "getElementsByName", "InnerScrollAndFocusHandler", "React", "Component", "componentDidMount", "handlePotentialScroll", "componentDidUpdate", "props", "focusAndScrollRef", "apply", "render", "children", "segmentPath", "segmentPaths", "some", "scrollRefSegmentPath", "index", "domNode", "Element", "HTMLElement", "parentElement", "localName", "nextElement<PERSON><PERSON>ling", "handleSmoothScroll", "scrollIntoView", "htmlElement", "documentElement", "clientHeight", "scrollTop", "dontForceLayout", "onlyHashChange", "focus", "ScrollAndFocusHandler", "context", "useContext", "GlobalLayoutRouterContext", "Error", "InnerLayoutRouter", "tree", "cacheNode", "url", "fullTree", "resolvedPrefetchRsc", "prefetchRsc", "rsc", "useDeferredValue", "resolvedRsc", "then", "use", "lazyData", "refetchTree", "includeNextUrl", "hasInterceptionRouteInCurrentTree", "navigatedAt", "Date", "now", "fetchServerResponse", "URL", "location", "origin", "flightRouterState", "nextUrl", "serverResponse", "startTransition", "dispatchAppRouterAction", "type", "ACTION_SERVER_PATCH", "previousTree", "unresolvedThenable", "subtree", "LayoutRouterContext", "Provider", "value", "parentTree", "parentCacheNode", "parentSegmentPath", "LoadingBoundary", "loading", "loadingModuleData", "promiseForLoading", "loadingRsc", "loadingStyles", "loadingScripts", "Suspense", "fallback", "parallel<PERSON><PERSON>er<PERSON>ey", "error", "errorStyles", "errorScripts", "templateStyles", "templateScripts", "template", "notFound", "forbidden", "unauthorized", "parentParallelRoutes", "parallelRoutes", "segmentMap", "get", "Map", "set", "parentTreeSegment", "treeSegment", "concat", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "stateKey", "newLazyCacheNode", "head", "prefetchHead", "TemplateContext", "Error<PERSON>ou<PERSON><PERSON>", "errorComponent", "HTTPAccessFallbackBoundary", "RedirectBoundary"], "mappings": "AAmIQyB,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAnIjC;;;;;+BAoe<PERSON>;;;CAGC,GACD,WAAA;;;eAAwB3B;;;;;;oCAzdjB;iEASA;mEACc;+CAKd;qCAC6B;oCACD;+BACL;+BACD;oCACM;kCACF;gCACU;sCACN;mDACa;gCACV;AAExC;;;CAGC,GACD,SAASC,eACPC,iBAAgD,EAChDC,cAAiC;IAEjC,IAAID,mBAAmB;QACrB,MAAM,CAACE,SAASC,iBAAiB,GAAGH;QACpC,MAAMI,SAASJ,kBAAkBK,MAAM,KAAK;QAE5C,IAAIC,CAAAA,GAAAA,eAAAA,YAAY,EAACL,cAAc,CAAC,EAAE,EAAEC,UAAU;YAC5C,IAAID,cAAc,CAAC,EAAE,CAACM,cAAc,CAACJ,mBAAmB;gBACtD,IAAIC,QAAQ;oBACV,MAAMI,UAAUT,eACdU,WACAR,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAErC,OAAO;wBACLF,cAAc,CAAC,EAAE;wBACjB;4BACE,GAAGA,cAAc,CAAC,EAAE;4BACpB,CAACE,iBAAiB,EAAE;gCAClBK,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACV;6BACD;wBACH;qBACD;gBACH;gBAEA,OAAO;oBACLP,cAAc,CAAC,EAAE;oBACjB;wBACE,GAAGA,cAAc,CAAC,EAAE;wBACpB,CAACE,iBAAiB,EAAEJ,eAClBC,kBAAkBU,KAAK,CAAC,IACxBT,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAEvC;iBACD;YACH;QACF;IACF;IAEA,OAAOF;AACT;AAEA,MAAMU,+DACJC,UAAAA,OAAQ,CACRD,4DAA4D;AAE9D,4FAA4F;AAC5F;;CAEC,GACD,SAASE,YACPC,QAAgD;IAEhD,+BAA+B;IAC/B,IAAI,OAAOC,WAAW,aAAa,OAAO;IAE1C,uGAAuG;IACvG,kCAAkC;IAClC,MAAMC,+BACJL,6DAA6DE,WAAW;IAC1E,OAAOG,6BAA6BF;AACtC;AAEA,MAAMG,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD;;CAEC,GACD,SAASC,kBAAkBC,OAAoB;IAC7C,kGAAkG;IAClG,0FAA0F;IAC1F,mDAAmD;IACnD,IAAI;QAAC;QAAU;KAAQ,CAACC,QAAQ,CAACC,iBAAiBF,SAASG,QAAQ,GAAG;QACpE,wCAA4C;YAC1CI,QAAQC,IAAI,CACV,4FACAR;QAEJ;QACA,OAAO;IACT;IAEA,2FAA2F;IAC3F,wDAAwD;IACxD,MAAMS,OAAOT,QAAQU,qBAAqB;IAC1C,OAAOZ,eAAea,KAAK,CAAC,CAACC,OAASH,IAAI,CAACG,KAAK,KAAK;AACvD;AAEA;;CAEC,GACD,SAASC,uBAAuBb,OAAoB,EAAEc,cAAsB;IAC1E,MAAML,OAAOT,QAAQU,qBAAqB;IAC1C,OAAOD,KAAKM,GAAG,IAAI,KAAKN,KAAKM,GAAG,IAAID;AACtC;AAEA;;;;;CAKC,GACD,SAASE,uBAAuBC,YAAoB;IAClD,+EAA+E;IAC/E,IAAIA,iBAAiB,OAAO;QAC1B,OAAOC,SAASC,IAAI;IACtB;QAIED;IAFF,qFAAqF;IACrF,OACEA,CAAAA,2BAAAA,SAASE,cAAc,CAACH,aAAAA,KAAAA,OAAxBC,2BACA,AACAA,SAASG,iBAAiB,CAACJ,aAAa,CAAC,EAAE,mDADmD;AAGlG;AAMA,MAAMK,mCAAmCC,OAAAA,OAAK,CAACC,SAAS;IA4GtDC,oBAAoB;QAClB,IAAI,CAACC,qBAAqB;IAC5B;IAEAC,qBAAqB;QACnB,sJAAsJ;QACtJ,IAAI,IAAI,CAACC,KAAK,CAACC,iBAAiB,CAACC,KAAK,EAAE;YACtC,IAAI,CAACJ,qBAAqB;QAC5B;IACF;IAEAK,SAAS;QACP,OAAO,IAAI,CAACH,KAAK,CAACI,QAAQ;IAC5B;;QAzHF,KAAA,IAAA,OAAA,IAAA,CACEN,qBAAAA,GAAwB;YACtB,qGAAqG;YACrG,MAAM,EAAEG,iBAAiB,EAAEI,WAAW,EAAE,GAAG,IAAI,CAACL,KAAK;YAErD,IAAIC,kBAAkBC,KAAK,EAAE;gBAC3B,uEAAuE;gBACvE,6EAA6E;gBAC7E,wEAAwE;gBACxE,IACED,kBAAkBK,YAAY,CAAChD,MAAM,KAAK,KAC1C,CAAC2C,kBAAkBK,YAAY,CAACC,IAAI,CAAC,CAACC,uBACpCH,YAAYtB,KAAK,CAAC,CAAC5B,SAASsD,QAC1BlD,CAAAA,GAAAA,eAAAA,YAAY,EAACJ,SAASqD,oBAAoB,CAACC,MAAM,KAGrD;oBACA;gBACF;gBAEA,IAAIC,UAEiC;gBACrC,MAAMrB,eAAeY,kBAAkBZ,YAAY;gBAEnD,IAAIA,cAAc;oBAChBqB,UAAUtB,uBAAuBC;gBACnC;gBAEA,kGAAkG;gBAClG,yEAAyE;gBACzE,IAAI,CAACqB,SAAS;oBACZA,UAAU5C,YAAY,IAAI;gBAC5B;gBAEA,uGAAuG;gBACvG,IAAI,CAAE4C,CAAAA,mBAAmBC,OAAM,GAAI;oBACjC;gBACF;gBAEA,4FAA4F;gBAC5F,2EAA2E;gBAC3E,MAAO,CAAED,CAAAA,mBAAmBE,WAAU,KAAMzC,kBAAkBuC,SAAU;oBACtE,IAAIlC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;4BACrCgC;wBAAJ,IAAIA,CAAAA,CAAAA,yBAAAA,QAAQG,aAAa,KAAA,OAAA,KAAA,IAArBH,uBAAuBI,SAAS,MAAK,QAAQ;wBAC/C,2FAA2F;wBAC3F,yEAAyE;wBACzE,iHAAiH;wBACnH;oBACF;oBAEA,uGAAuG;oBACvG,IAAIJ,QAAQK,kBAAkB,KAAK,MAAM;wBACvC;oBACF;oBACAL,UAAUA,QAAQK,kBAAkB;gBACtC;gBAEA,6EAA6E;gBAC7Ed,kBAAkBC,KAAK,GAAG;gBAC1BD,kBAAkBZ,YAAY,GAAG;gBACjCY,kBAAkBK,YAAY,GAAG,EAAE;gBAEnCU,CAAAA,GAAAA,oBAAAA,kBAAkB,EAChB;oBACE,uEAAuE;oBACvE,IAAI3B,cAAc;;wBACdqB,QAAwBO,cAAc;wBAExC;oBACF;oBACA,oFAAoF;oBACpF,4CAA4C;oBAC5C,MAAMC,cAAc5B,SAAS6B,eAAe;oBAC5C,MAAMjC,iBAAiBgC,YAAYE,YAAY;oBAE/C,oEAAoE;oBACpE,IAAInC,uBAAuByB,SAAwBxB,iBAAiB;wBAClE;oBACF;oBAEA,2FAA2F;oBAC3F,kHAAkH;oBAClH,qHAAqH;oBACrH,6HAA6H;oBAC7HgC,YAAYG,SAAS,GAAG;oBAExB,mFAAmF;oBACnF,IAAI,CAACpC,uBAAuByB,SAAwBxB,iBAAiB;wBACnE,0EAA0E;;wBACxEwB,QAAwBO,cAAc;oBAC1C;gBACF,GACA;oBACE,oDAAoD;oBACpDK,iBAAiB;oBACjBC,gBAAgBtB,kBAAkBsB,cAAc;gBAClD;gBAGF,wEAAwE;gBACxEtB,kBAAkBsB,cAAc,GAAG;gBAEnC,2BAA2B;gBAC3Bb,QAAQc,KAAK;YACf;QACF;;AAgBF;AAEA,SAASC,sBAAsB,KAM9B;IAN8B,IAAA,EAC7BpB,WAAW,EACXD,QAAQ,EAIT,GAN8B;IAO7B,MAAMsB,UAAUC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,+BAAAA,yBAAyB;IACpD,IAAI,CAACF,SAAS;QACZ,MAAM,OAAA,cAAuD,CAAvD,IAAIG,MAAM,+CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAsD;IAC9D;IAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACnC,4BAAAA;QACCW,aAAaA;QACbJ,mBAAmByB,QAAQzB,iBAAiB;kBAE3CG;;AAGP;AAEA;;CAEC,GACD,SAAS0B,kBAAkB,KAU1B;IAV0B,IAAA,EACzBC,IAAI,EACJ1B,WAAW,EACX2B,SAAS,EACTC,GAAG,EAMJ,GAV0B;IAWzB,MAAMP,UAAUC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,+BAAAA,yBAAyB;IACpD,IAAI,CAACF,SAAS;QACZ,MAAM,OAAA,cAAuD,CAAvD,IAAIG,MAAM,+CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAsD;IAC9D;IAEA,MAAM,EAAEE,MAAMG,QAAQ,EAAE,GAAGR;IAE3B,yDAAyD;IAEzD,4EAA4E;IAC5E,2EAA2E;IAC3E,iDAAiD;IACjD,EAAE;IACF,4EAA4E;IAC5E,MAAMS,sBACJH,UAAUI,WAAW,KAAK,OAAOJ,UAAUI,WAAW,GAAGJ,UAAUK,GAAG;IAExE,2EAA2E;IAC3E,2EAA2E;IAC3E,sCAAsC;IACtC,MAAMA,MAAWC,CAAAA,GAAAA,OAAAA,gBAAgB,EAACN,UAAUK,GAAG,EAAEF;IAEjD,wEAAwE;IACxE,2EAA2E;IAC3E,8EAA8E;IAC9E,mBAAmB;IACnB,MAAMI,cACJ,OAAOF,QAAQ,YAAYA,QAAQ,QAAQ,OAAOA,IAAIG,IAAI,KAAK,aAC3DC,CAAAA,GAAAA,OAAAA,GAAG,EAACJ,OACJA;IAEN,IAAI,CAACE,aAAa;QAChB,qEAAqE;QACrE,yEAAyE;QACzE,kCAAkC;QAElC,8CAA8C;QAC9C,IAAIG,WAAWV,UAAUU,QAAQ;QACjC,IAAIA,aAAa,MAAM;YACrB;;OAEC,GACD,sBAAsB;YACtB,MAAMC,cAAc3F,eAAe;gBAAC;mBAAOqD;aAAY,EAAE6B;YACzD,MAAMU,iBAAiBC,CAAAA,GAAAA,mCAAAA,iCAAiC,EAACX;YACzD,MAAMY,cAAcC,KAAKC,GAAG;YAC5BhB,UAAUU,QAAQ,GAAGA,WAAWO,CAAAA,GAAAA,qBAAAA,mBAAmB,EACjD,IAAIC,IAAIjB,KAAKkB,SAASC,MAAM,GAC5B;gBACEC,mBAAmBV;gBACnBW,SAASV,iBAAiBlB,QAAQ4B,OAAO,GAAG;YAC9C,GACAd,IAAI,CAAC,CAACe;gBACNC,CAAAA,GAAAA,OAAAA,eAAe,EAAC;oBACdC,CAAAA,GAAAA,gBAAAA,uBAAuB,EAAC;wBACtBC,MAAMC,oBAAAA,mBAAmB;wBACzBC,cAAc1B;wBACdqB;wBACAT;oBACF;gBACF;gBAEA,OAAOS;YACT;YAEA,gDAAgD;YAChDd,CAAAA,GAAAA,OAAAA,GAAG,EAACC;QACN;QACA,yGAAyG;QACzG,iIAAiI;QACjID,CAAAA,GAAAA,OAAAA,GAAG,EAACoB,oBAAAA,kBAAkB;IACxB;IAEA,yEAAyE;IACzE,MAAMC,UACJ,cACA,CAAA,GAAA,YAAA,GAAA,EAACC,+BAAAA,UAD2E,SACxD,CAACC,QAAQ,EAAA;QAC3BC,OAAO;YACLC,YAAYnC;YACZoC,iBAAiBnC;YACjBoC,mBAAmB/D;YAEnB,kDAAkD;YAClD4B,KAAKA;QACP;kBAECM;;IAGL,iFAAiF;IACjF,OAAOuB;AACT;AAEA;;;CAGC,GACD,SAASO,gBAAgB,KAMxB;IANwB,IAAA,EACvBC,OAAO,EACPlE,QAAQ,EAIT,GANwB;IAOvB,6EAA6E;IAC7E,4EAA4E;IAC5E,kDAAkD;IAClD,EAAE;IACF,sEAAsE;IACtE,4EAA4E;IAC5E,0EAA0E;IAC1E,8BAA8B;IAC9B,IAAImE;IACJ,IACE,OAAOD,YAAY,YACnBA,YAAY,QACZ,OAAQA,QAAgB9B,IAAI,KAAK,YACjC;QACA,MAAMgC,oBAAoBF;QAC1BC,oBAAoB9B,CAAAA,GAAAA,OAAAA,GAAG,EAAC+B;IAC1B,OAAO;QACLD,oBAAoBD;IACtB;IAEA,IAAIC,mBAAmB;QACrB,MAAME,aAAaF,iBAAiB,CAAC,EAAE;QACvC,MAAMG,gBAAgBH,iBAAiB,CAAC,EAAE;QAC1C,MAAMI,iBAAiBJ,iBAAiB,CAAC,EAAE;QAC3C,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACK,OAAAA,QAAQ,EAAA;YACPC,UAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAAA,YAAA,QAAA,EAAA;;oBACGH;oBACAC;oBACAF;;;sBAIJrE;;IAGP;IAEA,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAAA,YAAA,QAAA,EAAA;kBAAGA;;AACZ;AAMe,SAASrD,kBAAkB,KAsBzC;IAtByC,IAAA,EACxC+H,iBAAiB,EACjBC,KAAK,EACLC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,YAAY,EAYb,GAtByC;IAuBxC,MAAM7D,UAAUC,CAAAA,GAAAA,OAAAA,UAAU,EAACoC,+BAAAA,mBAAmB;IAC9C,IAAI,CAACrC,SAAS;QACZ,MAAM,OAAA,cAA2D,CAA3D,IAAIG,MAAM,mDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA0D;IAClE;IAEA,MAAM,EAAEqC,UAAU,EAAEC,eAAe,EAAEC,iBAAiB,EAAEnC,GAAG,EAAE,GAAGP;IAEhE,6EAA6E;IAC7E,aAAa;IACb,MAAM8D,uBAAuBrB,gBAAgBsB,cAAc;IAC3D,IAAIC,aAAaF,qBAAqBG,GAAG,CAACb;IAC1C,mEAAmE;IACnE,yJAAyJ;IACzJ,IAAI,CAACY,YAAY;QACfA,aAAa,IAAIE;QACjBJ,qBAAqBK,GAAG,CAACf,mBAAmBY;IAC9C;IAEA,qCAAqC;IACrC,8IAA8I;IAC9I,MAAMI,oBAAoB5B,UAAU,CAAC,EAAE;IACvC,MAAMnC,OAAOmC,UAAU,CAAC,EAAE,CAACY,kBAAkB;IAC7C,MAAMiB,cAAchE,IAAI,CAAC,EAAE;IAE3B,MAAM1B,cACJ+D,sBAAsB,OAGlB,AADA,qCACqC,iCADiC;IAEtE;QAACU;KAAkB,GACnBV,kBAAkB4B,MAAM,CAAC;QAACF;QAAmBhB;KAAkB;IAErE,8EAA8E;IAC9E,uEAAuE;IACvE,8EAA8E;IAC9E,6EAA6E;IAC7E,0DAA0D;IAC1D,EAAE;IACF,8EAA8E;IAC9E,2EAA2E;IAC3E,4EAA4E;IAC5E,yBAAyB;IACzB,MAAMmB,WAAWC,CAAAA,GAAAA,sBAAAA,oBAAoB,EAACH;IACtC,MAAMI,WAAWD,CAAAA,GAAAA,sBAAAA,oBAAoB,EAACH,aAAa,MAAM,mBAAmB;;IAE5E,yDAAyD;IACzD,IAAI/D,YAAY0D,WAAWC,GAAG,CAACM;IAC/B,IAAIjE,cAActE,WAAW;QAC3B,2EAA2E;QAC3E,sBAAsB;QACtB,MAAM0I,mBAAkC;YACtC1D,UAAU;YACVL,KAAK;YACLD,aAAa;YACbiE,MAAM;YACNC,cAAc;YACdb,gBAAgB,IAAIG;YACpBtB,SAAS;YACTxB,aAAa,CAAC;QAChB;QAEA,qEAAqE;QACrEd,YAAYoE;QACZV,WAAWG,GAAG,CAACI,UAAUG;IAC3B;IAEA;;;;;;;;EAQA,GAEA,4EAA4E;IAC5E,wEAAwE;IACxE,2EAA2E;IAC3E,2EAA2E;IAC3E,4EAA4E;IAC5E,0EAA0E;IAC1E,8EAA8E;IAC9E,6DAA6D;IAC7D,MAAM7B,oBAAoBJ,gBAAgBG,OAAO;IAEjD,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAACiC,+BAAAA,eAAe,CAACvC,QAAQ,EAAA;QAEvBC,OAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACxC,uBAAAA;YAAsBpB,aAAaA;sBAClC,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACmG,eAAAA,aAAa,EAAA;gBACZC,gBAAgB1B;gBAChBC,aAAaA;gBACbC,cAAcA;0BAEd,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACZ,iBAAAA;oBAAgBC,SAASC;8BACxB,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACmC,gBAAAA,0BAA0B,EAAA;wBACzBrB,UAAUA;wBACVC,WAAWA;wBACXC,cAAcA;kCAEd,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACoB,kBAAAA,gBAAgB,EAAA;sCACf,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAAC7E,mBAAAA;gCACCG,KAAKA;gCACLF,MAAMA;gCACNC,WAAWA;gCACX3B,aAAaA;;;;;;;;YAS1B6E;YACAC;YACAC;;OA9BIe;AAiCX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/client/components/render-from-template-context.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport { TemplateContext } from '../../shared/lib/app-router-context.shared-runtime'\n\nexport default function RenderFromTemplateContext(): JSX.Element {\n  const children = useContext(TemplateContext)\n  return <>{children}</>\n}\n"], "names": ["RenderFromTemplateContext", "children", "useContext", "TemplateContext"], "mappings": "AAAA;;;;;+BA<PERSON>,WAAA;;;eAAwBA;;;;;iEAHoB;+CACZ;AAEjB,SAASA;IACtB,MAAMC,WAAWC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,+BAAAA,eAAe;IAC3C,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAAA,YAAA,QAAA,EAAA;kBAAGF;;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/shared/lib/invariant-error.ts"], "sourcesContent": ["export class InvariantError extends Error {\n  constructor(message: string, options?: ErrorOptions) {\n    super(\n      `Invariant: ${message.endsWith('.') ? message : message + '.'} This is a bug in Next.js.`,\n      options\n    )\n    this.name = 'InvariantError'\n  }\n}\n"], "names": ["InvariantError", "Error", "constructor", "message", "options", "endsWith", "name"], "mappings": ";;;;+BAAaA,kBAAAA;;;eAAAA;;;AAAN,MAAMA,uBAAuBC;IAClCC,YAAYC,OAAe,EAAEC,OAAsB,CAAE;QACnD,KAAK,CACF,gBAAaD,CAAAA,QAAQE,QAAQ,CAAC,OAAOF,UAAUA,UAAU,GAAE,IAAE,8BAC9DC;QAEF,IAAI,CAACE,IAAI,GAAG;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/server/web/spec-extension/adapters/reflect.ts"], "sourcesContent": ["export class ReflectAdapter {\n  static get<T extends object>(\n    target: T,\n    prop: string | symbol,\n    receiver: unknown\n  ): any {\n    const value = Reflect.get(target, prop, receiver)\n    if (typeof value === 'function') {\n      return value.bind(target)\n    }\n\n    return value\n  }\n\n  static set<T extends object>(\n    target: T,\n    prop: string | symbol,\n    value: any,\n    receiver: any\n  ): boolean {\n    return Reflect.set(target, prop, value, receiver)\n  }\n\n  static has<T extends object>(target: T, prop: string | symbol): boolean {\n    return Reflect.has(target, prop)\n  }\n\n  static deleteProperty<T extends object>(\n    target: T,\n    prop: string | symbol\n  ): boolean {\n    return Reflect.deleteProperty(target, prop)\n  }\n}\n"], "names": ["ReflectAdapter", "get", "target", "prop", "receiver", "value", "Reflect", "bind", "set", "has", "deleteProperty"], "mappings": ";;;;+BAAaA,kBAAAA;;;eAAAA;;;AAAN,MAAMA;IACX,OAAOC,IACLC,MAAS,EACTC,IAAqB,EACrBC,QAAiB,EACZ;QACL,MAAMC,QAAQC,QAAQL,GAAG,CAACC,QAAQC,MAAMC;QACxC,IAAI,OAAOC,UAAU,YAAY;YAC/B,OAAOA,MAAME,IAAI,CAACL;QACpB;QAEA,OAAOG;IACT;IAEA,OAAOG,IACLN,MAAS,EACTC,IAAqB,EACrBE,KAAU,EACVD,QAAa,EACJ;QACT,OAAOE,QAAQE,GAAG,CAACN,QAAQC,MAAME,OAAOD;IAC1C;IAEA,OAAOK,IAAsBP,MAAS,EAAEC,IAAqB,EAAW;QACtE,OAAOG,QAAQG,GAAG,CAACP,QAAQC;IAC7B;IAEA,OAAOO,eACLR,MAAS,EACTC,IAAqB,EACZ;QACT,OAAOG,QAAQI,cAAc,CAACR,QAAQC;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/shared/lib/utils/reflect-utils.ts"], "sourcesContent": ["// This regex will have fast negatives meaning valid identifiers may not pass\n// this test. However this is only used during static generation to provide hints\n// about why a page bailed out of some or all prerendering and we can use bracket notation\n// for example while `ಠ_ಠ` is a valid identifier it's ok to print `searchParams['ಠ_ಠ']`\n// even if this would have been fine too `searchParams.ಠ_ಠ`\nconst isDefinitelyAValidIdentifier = /^[A-Za-z_$][A-Za-z0-9_$]*$/\n\nexport function describeStringPropertyAccess(target: string, prop: string) {\n  if (isDefinitelyAValidIdentifier.test(prop)) {\n    return `\\`${target}.${prop}\\``\n  }\n  return `\\`${target}[${JSON.stringify(prop)}]\\``\n}\n\nexport function describeHasCheckingStringProperty(\n  target: string,\n  prop: string\n) {\n  const stringifiedProp = JSON.stringify(prop)\n  return `\\`Reflect.has(${target}, ${stringifiedProp})\\`, \\`${stringifiedProp} in ${target}\\`, or similar`\n}\n\nexport const wellKnownProperties = new Set([\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toString',\n  'valueOf',\n  'toLocaleString',\n\n  // Promise prototype\n  // fallthrough\n  'then',\n  'catch',\n  'finally',\n\n  // React Promise extension\n  // fallthrough\n  'status',\n\n  // React introspection\n  'displayName',\n\n  // Common tested properties\n  // fallthrough\n  'toJSON',\n  '$$typeof',\n  '__esModule',\n])\n"], "names": ["describeHasCheckingStringProperty", "describeStringPropertyAccess", "wellKnownProperties", "isDefinitelyAValidIdentifier", "target", "prop", "test", "JSON", "stringify", "stringifiedProp", "Set"], "mappings": "AAAA,6EAA6E;AAC7E,iFAAiF;AACjF,0FAA0F;AAC1F,uFAAuF;AACvF,2DAA2D;;;;;;;;;;;;;;;;;IAU3CA,iCAAiC,EAAA;eAAjCA;;IAPAC,4BAA4B,EAAA;eAA5BA;;IAeHC,mBAAmB,EAAA;eAAnBA;;;AAjBb,MAAMC,+BAA+B;AAE9B,SAASF,6BAA6BG,MAAc,EAAEC,IAAY;IACvE,IAAIF,6BAA6BG,IAAI,CAACD,OAAO;QAC3C,OAAQ,MAAID,SAAO,MAAGC,OAAK;IAC7B;IACA,OAAQ,MAAID,SAAO,MAAGG,KAAKC,SAAS,CAACH,QAAM;AAC7C;AAEO,SAASL,kCACdI,MAAc,EACdC,IAAY;IAEZ,MAAMI,kBAAkBF,KAAKC,SAAS,CAACH;IACvC,OAAQ,kBAAgBD,SAAO,OAAIK,kBAAgB,UAASA,kBAAgB,SAAML,SAAO;AAC3F;AAEO,MAAMF,sBAAsB,IAAIQ,IAAI;IACzC;IACA;IACA;IACA;IACA;IACA;IAEA,oBAAoB;IACpB,cAAc;IACd;IACA;IACA;IAEA,0BAA0B;IAC1B,cAAc;IACd;IAEA,sBAAsB;IACtB;IAEA,2BAA2B;IAC3B,cAAc;IACd;IACA;IACA;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/client/request/search-params.browser.dev.ts"], "sourcesContent": ["import type { SearchParams } from '../../server/request/search-params'\n\nimport { ReflectAdapter } from '../../server/web/spec-extension/adapters/reflect'\nimport {\n  describeStringPropertyAccess,\n  describeHasCheckingStringProperty,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nexport function makeUntrackedExoticSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingSearchParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          warnForSyncAccess(expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          warnForSyncAccess(expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      warnForSyncSpread()\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction warnForSyncAccess(expression: string) {\n  console.error(\n    `A searchParam property was accessed directly with ${expression}. ` +\n      `\\`searchParams\\` should be unwrapped with \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction warnForSyncSpread() {\n  console.error(\n    `The keys of \\`searchParams\\` were accessed directly. ` +\n      `\\`searchParams\\` should be unwrapped with \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n"], "names": ["makeUntrackedExoticSearchParamsWithDevWarnings", "CachedSearchParams", "WeakMap", "underlyingSearchParams", "cachedSearchParams", "get", "proxiedProperties", "Set", "unproxiedProperties", "promise", "Promise", "resolve", "Object", "keys", "for<PERSON>ach", "prop", "wellKnownProperties", "has", "push", "add", "proxiedPromise", "Proxy", "target", "receiver", "Reflect", "expression", "describeStringPropertyAccess", "warnForSyncAccess", "ReflectAdapter", "set", "value", "delete", "describeHasCheckingStringProperty", "ownKeys", "warnForSyncSpread", "console", "error"], "mappings": ";;;;+BAYg<PERSON>,kDAAAA;;;eAAAA;;;yBAVe;8BAKxB;AAGP,MAAMC,qBAAqB,IAAIC;AAExB,SAASF,+CACdG,sBAAoC;IAEpC,MAAMC,qBAAqBH,mBAAmBI,GAAG,CAACF;IAClD,IAAIC,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7C,MAAMC,UAAUC,QAAQC,OAAO,CAACR;IAEhCS,OAAOC,IAAI,CAACV,wBAAwBW,OAAO,CAAC,CAACC;QAC3C,IAAIC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEP,oBAAoBU,IAAI,CAACH;QAC3B,OAAO;YACLT,kBAAkBa,GAAG,CAACJ;YACpBN,OAAe,CAACM,KAAK,GAAGZ,sBAAsB,CAACY,KAAK;QACxD;IACF;IAEA,MAAMK,iBAAiB,IAAIC,MAAMZ,SAAS;QACxCJ,KAAIiB,MAAM,EAAEP,IAAI,EAAEQ,QAAQ;YACxB,IAAI,OAAOR,SAAS,UAAU;gBAC5B,IACE,CAACC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,SACxBT,CAAAA,kBAAkBW,GAAG,CAACF,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BS,QAAQP,GAAG,CAACK,QAAQP,UAAU,KAAI,GACpC;oBACA,MAAMU,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,gBAAgBX;oBAChEY,kBAAkBF;gBACpB;YACF;YACA,OAAOG,SAAAA,cAAc,CAACvB,GAAG,CAACiB,QAAQP,MAAMQ;QAC1C;QACAM,KAAIP,MAAM,EAAEP,IAAI,EAAEe,KAAK,EAAEP,QAAQ;YAC/B,IAAI,OAAOR,SAAS,UAAU;gBAC5BT,kBAAkByB,MAAM,CAAChB;YAC3B;YACA,OAAOS,QAAQK,GAAG,CAACP,QAAQP,MAAMe,OAAOP;QAC1C;QACAN,KAAIK,MAAM,EAAEP,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,CAACC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,SACxBT,CAAAA,kBAAkBW,GAAG,CAACF,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BS,QAAQP,GAAG,CAACK,QAAQP,UAAU,KAAI,GACpC;oBACA,MAAMU,aAAaO,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAjB;oBAEFY,kBAAkBF;gBACpB;YACF;YACA,OAAOD,QAAQP,GAAG,CAACK,QAAQP;QAC7B;QACAkB,SAAQX,MAAM;YACZY;YACA,OAAOV,QAAQS,OAAO,CAACX;QACzB;IACF;IAEArB,mBAAmB4B,GAAG,CAAC1B,wBAAwBiB;IAC/C,OAAOA;AACT;AAEA,SAASO,kBAAkBF,UAAkB;IAC3CU,QAAQC,KAAK,CACV,uDAAoDX,aAAW,OAC7D,4FACA;AAEP;AAEA,SAASS;IACPC,QAAQC,KAAK,CACV,wDACE,4FACA;AAEP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 776, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/client/request/search-params.browser.ts"], "sourcesContent": ["export const createRenderSearchParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (\n        require('./search-params.browser.dev') as typeof import('./search-params.browser.dev')\n      ).makeUntrackedExoticSearchParamsWithDevWarnings\n    : (\n        require('./search-params.browser.prod') as typeof import('./search-params.browser.prod')\n      ).makeUntrackedExoticSearchParams\n"], "names": ["createRenderSearchParamsFromClient", "process", "env", "NODE_ENV", "require", "makeUntrackedExoticSearchParamsWithDevWarnings", "makeUntrackedExoticSearchParams"], "mappings": "AACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BADdH,sCAAAA;;;eAAAA;;;AAAN,MAAMA,4EAGLI,QAAQ,0HACRC,8CAA8C,GAChD,AACED,QAAQ,gCACRE,+BAA+B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 800, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/client/request/params.browser.dev.ts"], "sourcesContent": ["import type { Params } from '../../server/request/params'\n\nimport { ReflectAdapter } from '../../server/web/spec-extension/adapters/reflect'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nexport function makeDynamicallyTrackedExoticParamsWithDevWarnings(\n  underlyingParams: Params\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingParams)\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          warnForSyncAccess(expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      warnForEnumeration(unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction warnForSyncAccess(expression: string) {\n  console.error(\n    `A param property was accessed directly with ${expression}. \\`params\\` is now a Promise and should be unwrapped with \\`React.use()\\` before accessing properties of the underlying params object. In this version of Next.js direct access to param properties is still supported to facilitate migration but in a future version you will be required to unwrap \\`params\\` with \\`React.use()\\`.`\n  )\n}\n\nfunction warnForEnumeration(missingProperties: Array<string>) {\n  if (missingProperties.length) {\n    const describedMissingProperties =\n      describeListOfPropertyNames(missingProperties)\n    console.error(\n      `params are being enumerated incompletely missing these properties: ${describedMissingProperties}. ` +\n        `\\`params\\` should be unwrapped with \\`React.use()\\` before using its value. ` +\n        `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n    )\n  } else {\n    console.error(\n      `params are being enumerated. ` +\n        `\\`params\\` should be unwrapped with \\`React.use()\\` before using its value. ` +\n        `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n    )\n  }\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n"], "names": ["makeDynamicallyTrackedExoticParamsWithDevWarnings", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "underlyingParams", "cachedParams", "get", "promise", "Promise", "resolve", "proxiedProperties", "Set", "unproxiedProperties", "Object", "keys", "for<PERSON>ach", "prop", "wellKnownProperties", "has", "add", "proxiedPromise", "Proxy", "target", "receiver", "expression", "describeStringPropertyAccess", "warnForSyncAccess", "ReflectAdapter", "set", "value", "delete", "ownKeys", "warnForEnumeration", "Reflect", "console", "error", "missingProperties", "length", "describedMissingProperties", "describeListOfPropertyNames", "properties", "InvariantError", "description", "i"], "mappings": ";;;;+BAYgBA,qDAAAA;;;eAAAA;;;yBAVe;gCACA;8BAIxB;AAGP,MAAMC,eAAe,IAAIC;AAElB,SAASF,kDACdG,gBAAwB;IAExB,MAAMC,eAAeH,aAAaI,GAAG,CAACF;IACtC,IAAIC,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUC,QAAQC,OAAO,CAACL;IAEhC,MAAMM,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7CC,OAAOC,IAAI,CAACV,kBAAkBW,OAAO,CAAC,CAACC;QACrC,IAAIC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACLN,kBAAkBS,GAAG,CAACH;YACpBT,OAAe,CAACS,KAAK,GAAGZ,gBAAgB,CAACY,KAAK;QAClD;IACF;IAEA,MAAMI,iBAAiB,IAAIC,MAAMd,SAAS;QACxCD,KAAIgB,MAAM,EAAEN,IAAI,EAAEO,QAAQ;YACxB,IAAI,OAAOP,SAAS,UAAU;gBAC5B,IACE,AACAN,kBAAkBQ,GAAG,CAACF,OACtB,0CAFuE;oBAGvE,MAAMQ,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUT;oBAC1DU,kBAAkBF;gBACpB;YACF;YACA,OAAOG,SAAAA,cAAc,CAACrB,GAAG,CAACgB,QAAQN,MAAMO;QAC1C;QACAK,KAAIN,MAAM,EAAEN,IAAI,EAAEa,KAAK,EAAEN,QAAQ;YAC/B,IAAI,OAAOP,SAAS,UAAU;gBAC5BN,kBAAkBoB,MAAM,CAACd;YAC3B;YACA,OAAOW,SAAAA,cAAc,CAACC,GAAG,CAACN,QAAQN,MAAMa,OAAON;QACjD;QACAQ,SAAQT,MAAM;YACZU,mBAAmBpB;YACnB,OAAOqB,QAAQF,OAAO,CAACT;QACzB;IACF;IAEApB,aAAa0B,GAAG,CAACxB,kBAAkBgB;IACnC,OAAOA;AACT;AAEA,SAASM,kBAAkBF,UAAkB;IAC3CU,QAAQC,KAAK,CACV,iDAA8CX,aAAW;AAE9D;AAEA,SAASQ,mBAAmBI,iBAAgC;IAC1D,IAAIA,kBAAkBC,MAAM,EAAE;QAC5B,MAAMC,6BACJC,4BAA4BH;QAC9BF,QAAQC,KAAK,CACV,wEAAqEG,6BAA2B,OAC9F,6EACA;IAEP,OAAO;QACLJ,QAAQC,KAAK,CACV,kCACE,6EACA;IAEP;AACF;AAEA,SAASI,4BAA4BC,UAAyB;IAC5D,OAAQA,WAAWH,MAAM;QACvB,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,IAAII,gBAAAA,cAAc,CACtB,wFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YACH,OAAQ,MAAID,UAAU,CAAC,EAAE,GAAC;QAC5B,KAAK;YACH,OAAQ,MAAIA,UAAU,CAAC,EAAE,GAAC,YAAWA,UAAU,CAAC,EAAE,GAAC;QACrD;YAAS;gBACP,IAAIE,cAAc;gBAClB,IAAK,IAAIC,IAAI,GAAGA,IAAIH,WAAWH,MAAM,GAAG,GAAGM,IAAK;oBAC9CD,eAAgB,MAAIF,UAAU,CAACG,EAAE,GAAC;gBACpC;gBACAD,eAAgB,YAAUF,UAAU,CAACA,WAAWH,MAAM,GAAG,EAAE,GAAC;gBAC5D,OAAOK;YACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 904, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/client/request/params.browser.ts"], "sourcesContent": ["export const createRenderParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (require('./params.browser.dev') as typeof import('./params.browser.dev'))\n        .makeDynamicallyTrackedExoticParamsWithDevWarnings\n    : (\n        require('./params.browser.prod') as typeof import('./params.browser.prod')\n      ).makeUntrackedExoticParams\n"], "names": ["createRenderParamsFromClient", "process", "env", "NODE_ENV", "require", "makeDynamicallyTrackedExoticParamsWithDevWarnings", "makeUntrackedExoticParams"], "mappings": "AACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BADdH,gCAAAA;;;eAAAA;;;AAAN,MAAMA,sEAENI,QAAQ,mHACNC,iDAAiD,GACpD,AACED,QAAQ,yBACRE,yBAAyB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 928, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/server/create-deduped-by-callsite-server-error-logger.ts"], "sourcesContent": ["import * as React from 'react'\n\nconst errorRef: { current: null | Error } = { current: null }\n\n// React.cache is currently only available in canary/experimental React channels.\nconst cache =\n  typeof React.cache === 'function'\n    ? React.cache\n    : (fn: (key: unknown) => void) => fn\n\n// When Dynamic IO is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn = process.env.__NEXT_DYNAMIC_IO\n  ? console.error\n  : console.warn\n\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n  (key: unknown) => {\n    try {\n      logErrorOrWarn(errorRef.current)\n    } finally {\n      errorRef.current = null\n    }\n  }\n)\n\n/**\n * Creates a function that logs an error message that is deduped by the userland\n * callsite.\n * This requires no indirection between the call of this function and the userland\n * callsite i.e. there's only a single library frame above this.\n * Do not use on the Client where sourcemaps and ignore listing might be enabled.\n * Only use that for warnings need a fix independent of the callstack.\n *\n * @param getMessage\n * @returns\n */\nexport function createDedupedByCallsiteServerErrorLoggerDev<Args extends any[]>(\n  getMessage: (...args: Args) => Error\n) {\n  return function logDedupedError(...args: Args) {\n    const message = getMessage(...args)\n\n    if (process.env.NODE_ENV !== 'production') {\n      const callStackFrames = new Error().stack?.split('\\n')\n      if (callStackFrames === undefined || callStackFrames.length < 4) {\n        logErrorOrWarn(message)\n      } else {\n        // Error:\n        //   logDedupedError\n        //   asyncApiBeingAccessedSynchronously\n        //   <userland callsite>\n        // TODO: This breaks if sourcemaps with ignore lists are enabled.\n        const key = callStackFrames[4]\n        errorRef.current = message\n        flushCurrentErrorIfNew(key)\n      }\n    } else {\n      logErrorOrWarn(message)\n    }\n  }\n}\n"], "names": ["createDedupedByCallsiteServerErrorLoggerDev", "errorRef", "current", "cache", "React", "fn", "logErrorOrWarn", "process", "env", "__NEXT_DYNAMIC_IO", "console", "error", "warn", "flushCurrentErrorIfNew", "key", "getMessage", "logDedupedError", "args", "message", "NODE_ENV", "callStackFrames", "Error", "stack", "split", "undefined", "length"], "mappings": "AAauBO,QAAQC,GAAG,CAACC,iBAAiB,GAChDC,QAAQC,KAAK;;;;;+BA2BDX,+CAAAA;;;eAAAA;;;+DAzCO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvB,MAAMC,WAAsC;IAAEC,SAAS;AAAK;AAE5D,iFAAiF;AACjF,MAAMC,QACJ,OAAOC,OAAMD,KAAK,KAAK,aACnBC,OAAMD,KAAK,GACX,CAACE,KAA+BA;AAEtC,qEAAqE;AACrE,qEAAqE;AACrE,gBAAgB;AAChB,MAAMC,8FAEFI,QAAQE,IAAI;AAEhB,2CAA2C;AAC3C,wGAAwG;AACxG,MAAMC,yBAAyBV,MAC7B,AACA,CAACW,yEADyE;IAExE,IAAI;QACFR,eAAeL,SAASC,OAAO;IACjC,SAAU;QACRD,SAASC,OAAO,GAAG;IACrB;AACF;AAcK,SAASF,4CACde,UAAoC;IAEpC,OAAO,SAASC,gBAAgB,GAAGC,IAAU;QAC3C,MAAMC,UAAUH,cAAcE;QAE9B,IAAIV,QAAQC,GAAG,CAACW,QAAQ,KAAK,WAAc;gBACjB;YAAxB,MAAMC,kBAAAA,CAAkB,SAAA,IAAIC,QAAQC,KAAK,KAAA,OAAA,KAAA,IAAjB,OAAmBC,KAAK,CAAC;YACjD,IAAIH,oBAAoBI,aAAaJ,gBAAgBK,MAAM,GAAG,GAAG;gBAC/DnB,eAAeY;YACjB,OAAO;gBACL,SAAS;gBACT,oBAAoB;gBACpB,uCAAuC;gBACvC,wBAAwB;gBACxB,iEAAiE;gBACjE,MAAMJ,MAAMM,eAAe,CAAC,EAAE;gBAC9BnB,SAASC,OAAO,GAAGgB;gBACnBL,uBAAuBC;YACzB;QACF,OAAO;;QAEP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1027, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/server/app-render/after-task-async-storage-instance.ts"], "sourcesContent": ["import type { AfterTaskAsyncStorage } from './after-task-async-storage.external'\nimport { createAsyncLocalStorage } from './async-local-storage'\n\nexport const afterTaskAsyncStorageInstance: AfterTaskAsyncStorage =\n  createAsyncLocalStorage()\n"], "names": ["afterTaskAsyncStorageInstance", "createAsyncLocalStorage"], "mappings": ";;;;+BAGaA,iCAAAA;;;eAAAA;;;mCAF2B;AAEjC,MAAMA,gCACXC,CAAAA,GAAAA,mBAAAA,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1044, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/server/app-render/after-task-async-storage.external.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\n\n// Share the instance module in the next-shared layer\nimport { afterTaskAsyncStorageInstance as afterTaskAsyncStorage } from './after-task-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\nimport type { WorkUnitStore } from './work-unit-async-storage.external'\n\nexport interface AfterTaskStore {\n  /** The phase in which the topmost `after` was called.\n   *\n   * NOTE: Can be undefined when running `generateStaticParams`,\n   * where we only have a `workStore`, no `workUnitStore`.\n   */\n  readonly rootTaskSpawnPhase: WorkUnitStore['phase'] | undefined\n}\n\nexport type AfterTaskAsyncStorage = AsyncLocalStorage<AfterTaskStore>\n\nexport { afterTaskAsyncStorage }\n"], "names": ["afterTaskAsyncStorage"], "mappings": ";;;;+BAiBSA,yBAAAA;;;eAAAA,+BAAAA,6BAAqB;;;+CAdyC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1060, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/server/request/utils.ts"], "sourcesContent": ["import { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { afterTaskAsyncStorage } from '../app-render/after-task-async-storage.external'\nimport type { WorkStore } from '../app-render/work-async-storage.external'\n\nexport function throwWithStaticGenerationBailoutError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwWithStaticGenerationBailoutErrorWithDynamicError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwForSearchParamsAccessInUseCache(\n  workStore: WorkStore\n): never {\n  const error = new Error(\n    `Route ${workStore.route} used \"searchParams\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"searchParams\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n  )\n\n  workStore.invalidUsageError ??= error\n\n  throw error\n}\n\nexport function isRequestAPICallableInsideAfter() {\n  const afterTaskStore = afterTaskAsyncStorage.getStore()\n  return afterTaskStore?.rootTaskSpawnPhase === 'action'\n}\n"], "names": ["isRequestAPICallableInsideAfter", "throwForSearchParamsAccessInUseCache", "throwWithStaticGenerationBailoutError", "throwWithStaticGenerationBailoutErrorWithDynamicError", "route", "expression", "StaticGenBailoutError", "workStore", "error", "Error", "invalidUsageError", "afterTaskStore", "afterTaskAsyncStorage", "getStore", "rootTaskSpawnPhase"], "mappings": ";;;;;;;;;;;;;;;;;IAkCgBA,+BAA+B,EAAA;eAA/BA;;IAZAC,oCAAoC,EAAA;eAApCA;;IAlBAC,qCAAqC,EAAA;eAArCA;;IASAC,qDAAqD,EAAA;eAArDA;;;yCAbsB;+CACA;AAG/B,SAASD,sCACdE,KAAa,EACbC,UAAkB;IAElB,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEF,MAAM,iDAAiD,EAAEC,WAAW,0HAA0H,CAAC,GADpM,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASF,sDACdC,KAAa,EACbC,UAAkB;IAElB,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEF,MAAM,4EAA4E,EAAEC,WAAW,0HAA0H,CAAC,GAD/N,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASJ,qCACdM,SAAoB;IAEpB,MAAMC,QAAQ,OAAA,cAEb,CAFa,IAAIC,MAChB,CAAC,MAAM,EAAEF,UAAUH,KAAK,CAAC,oVAAoV,CAAC,GADlW,qBAAA;eAAA;oBAAA;sBAAA;IAEd;IAEAG,UAAUG,iBAAiB,KAAKF;IAEhC,MAAMA;AACR;AAEO,SAASR;IACd,MAAMW,iBAAiBC,+BAAAA,qBAAqB,CAACC,QAAQ;IACrD,OAAOF,CAAAA,kBAAAA,OAAAA,KAAAA,IAAAA,eAAgBG,kBAAkB,MAAK;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/server/request/search-params.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  trackDynamicDataInDynamicRender,\n  annotateDynamicAccess,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n  type PrerenderStoreLegacy,\n  type PrerenderStorePPR,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport {\n  describeStringPropertyAccess,\n  describeHasCheckingStringProperty,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport {\n  throwWithStaticGenerationBailoutErrorWithDynamicError,\n  throwForSearchParamsAccessInUseCache,\n} from './utils'\nimport { scheduleImmediate } from '../../lib/scheduler'\n\nexport type SearchParams = { [key: string]: string | string[] | undefined }\n\n/**\n * In this version of Next.js the `params` prop passed to Layouts, Pages, and other Segments is a Promise.\n * However to facilitate migration to this new Promise type you can currently still access params directly on the Promise instance passed to these Segments.\n * The `UnsafeUnwrappedSearchParams` type is available if you need to temporarily access the underlying params without first awaiting or `use`ing the Promise.\n *\n * In a future version of Next.js the `params` prop will be a plain Promise and this type will be removed.\n *\n * Typically instances of `params` can be updated automatically to be treated as a Promise by a codemod published alongside this Next.js version however if you\n * have not yet run the codemod of the codemod cannot detect certain instances of `params` usage you should first try to refactor your code to await `params`.\n *\n * If refactoring is not possible but you still want to be able to access params directly without typescript errors you can cast the params Promise to this type\n *\n * ```tsx\n * type Props = { searchParams: Promise<{ foo: string }> }\n *\n * export default async function Page(props: Props) {\n *  const { searchParams } = (props.searchParams as unknown as UnsafeUnwrappedSearchParams<typeof props.searchParams>)\n *  return ...\n * }\n * ```\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedSearchParams<P> =\n  P extends Promise<infer U> ? Omit<U, 'then' | 'status' | 'value'> : never\n\nexport function createSearchParamsFromClient(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderSearchParams(workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderSearchParams(underlyingSearchParams, workStore)\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport const createServerSearchParamsForMetadata =\n  createServerSearchParamsForServerPage\n\nexport function createServerSearchParamsForServerPage(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderSearchParams(workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderSearchParams(underlyingSearchParams, workStore)\n}\n\nexport function createPrerenderSearchParamsForClientPage(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (prerenderStore && prerenderStore.type === 'prerender') {\n    // dynamicIO Prerender\n    // We're prerendering in a mode that aborts (dynamicIO) and should stall\n    // the promise to ensure the RSC side is considered dynamic\n    return makeHangingPromise(prerenderStore.renderSignal, '`searchParams`')\n  }\n  // We're prerendering in a mode that does not aborts. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve({})\n}\n\nfunction createPrerenderSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  if (prerenderStore.type === 'prerender') {\n    // We are in a dynamicIO (PPR or otherwise) prerender\n    return makeAbortingExoticSearchParams(workStore.route, prerenderStore)\n  }\n\n  // The remaining cases are prerender-ppr and prerender-legacy\n  // We are in a legacy static generation and need to interrupt the prerender\n  // when search params are accessed.\n  return makeErroringExoticSearchParams(workStore, prerenderStore)\n}\n\nfunction createRenderSearchParams(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  } else {\n    if (\n      process.env.NODE_ENV === 'development' &&\n      !workStore.isPrefetchRequest\n    ) {\n      return makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(\n        underlyingSearchParams,\n        workStore\n      )\n    } else {\n      return makeUntrackedExoticSearchParams(underlyingSearchParams, workStore)\n    }\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nconst CachedSearchParamsForUseCache = new WeakMap<\n  CacheLifetime,\n  Promise<SearchParams>\n>()\n\nfunction makeAbortingExoticSearchParams(\n  route: string,\n  prerenderStore: PrerenderStoreModern\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(prerenderStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = makeHangingPromise<SearchParams>(\n    prerenderStore.renderSignal,\n    '`searchParams`'\n  )\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      switch (prop) {\n        case 'then': {\n          const expression =\n            '`await searchParams`, `searchParams.then`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n        case 'status': {\n          const expression =\n            '`use(searchParams)`, `searchParams.status`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        default: {\n          if (typeof prop === 'string' && !wellKnownProperties.has(prop)) {\n            const expression = describeStringPropertyAccess(\n              'searchParams',\n              prop\n            )\n            const error = createSearchAccessError(route, expression)\n            abortAndThrowOnSynchronousRequestDataAccess(\n              route,\n              expression,\n              error,\n              prerenderStore\n            )\n          }\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n      }\n    },\n    has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests trigger dynamic. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (typeof prop === 'string') {\n        const expression = describeHasCheckingStringProperty(\n          'searchParams',\n          prop\n        )\n        const error = createSearchAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      }\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys() {\n      const expression =\n        '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n      const error = createSearchAccessError(route, expression)\n      abortAndThrowOnSynchronousRequestDataAccess(\n        route,\n        expression,\n        error,\n        prerenderStore\n      )\n    },\n  })\n\n  CachedSearchParams.set(prerenderStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeErroringExoticSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreLegacy | PrerenderStorePPR\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const underlyingSearchParams = {}\n  // For search params we don't construct a ReactPromise because we want to interrupt\n  // rendering on any property access that was not set from outside and so we only want\n  // to have properties like value and status if React sets them.\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      switch (prop) {\n        case 'then': {\n          const expression =\n            '`await searchParams`, `searchParams.then`, or similar'\n          if (workStore.dynamicShouldError) {\n            throwWithStaticGenerationBailoutErrorWithDynamicError(\n              workStore.route,\n              expression\n            )\n          } else if (prerenderStore.type === 'prerender-ppr') {\n            // PPR Prerender (no dynamicIO)\n            postponeWithTracking(\n              workStore.route,\n              expression,\n              prerenderStore.dynamicTracking\n            )\n          } else {\n            // Legacy Prerender\n            throwToInterruptStaticGeneration(\n              expression,\n              workStore,\n              prerenderStore\n            )\n          }\n          return\n        }\n        case 'status': {\n          const expression =\n            '`use(searchParams)`, `searchParams.status`, or similar'\n          if (workStore.dynamicShouldError) {\n            throwWithStaticGenerationBailoutErrorWithDynamicError(\n              workStore.route,\n              expression\n            )\n          } else if (prerenderStore.type === 'prerender-ppr') {\n            // PPR Prerender (no dynamicIO)\n            postponeWithTracking(\n              workStore.route,\n              expression,\n              prerenderStore.dynamicTracking\n            )\n          } else {\n            // Legacy Prerender\n            throwToInterruptStaticGeneration(\n              expression,\n              workStore,\n              prerenderStore\n            )\n          }\n          return\n        }\n        default: {\n          if (typeof prop === 'string' && !wellKnownProperties.has(prop)) {\n            const expression = describeStringPropertyAccess(\n              'searchParams',\n              prop\n            )\n            if (workStore.dynamicShouldError) {\n              throwWithStaticGenerationBailoutErrorWithDynamicError(\n                workStore.route,\n                expression\n              )\n            } else if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          }\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n      }\n    },\n    has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests trigger dynamic. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (typeof prop === 'string') {\n        const expression = describeHasCheckingStringProperty(\n          'searchParams',\n          prop\n        )\n        if (workStore.dynamicShouldError) {\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            workStore.route,\n            expression\n          )\n        } else if (prerenderStore.type === 'prerender-ppr') {\n          // PPR Prerender (no dynamicIO)\n          postponeWithTracking(\n            workStore.route,\n            expression,\n            prerenderStore.dynamicTracking\n          )\n        } else {\n          // Legacy Prerender\n          throwToInterruptStaticGeneration(\n            expression,\n            workStore,\n            prerenderStore\n          )\n        }\n        return false\n      }\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys() {\n      const expression =\n        '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n      if (workStore.dynamicShouldError) {\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          workStore.route,\n          expression\n        )\n      } else if (prerenderStore.type === 'prerender-ppr') {\n        // PPR Prerender (no dynamicIO)\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          prerenderStore.dynamicTracking\n        )\n      } else {\n        // Legacy Prerender\n        throwToInterruptStaticGeneration(expression, workStore, prerenderStore)\n      }\n    },\n  })\n\n  CachedSearchParams.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\n/**\n * This is a variation of `makeErroringExoticSearchParams` that always throws an\n * error on access, because accessing searchParams inside of `\"use cache\"` is\n * not allowed.\n */\nexport function makeErroringExoticSearchParamsForUseCache(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParamsForUseCache.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = Promise.resolve({})\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it. We know it\n        // isn't a dynamic access because it can only be something that was\n        // previously written to the promise and thus not an underlying\n        // searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      if (\n        typeof prop === 'string' &&\n        (prop === 'then' || !wellKnownProperties.has(prop))\n      ) {\n        throwForSearchParamsAccessInUseCache(workStore)\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests throw an error. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (\n        typeof prop === 'string' &&\n        (prop === 'then' || !wellKnownProperties.has(prop))\n      ) {\n        throwForSearchParamsAccessInUseCache(workStore)\n      }\n\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys() {\n      throwForSearchParamsAccessInUseCache(workStore)\n    },\n  })\n\n  CachedSearchParamsForUseCache.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeUntrackedExoticSearchParams(\n  underlyingSearchParams: SearchParams,\n  store: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingSearchParams)\n  CachedSearchParams.set(underlyingSearchParams, promise)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (!wellKnownProperties.has(prop)) {\n      Object.defineProperty(promise, prop, {\n        get() {\n          const workUnitStore = workUnitAsyncStorage.getStore()\n          trackDynamicDataInDynamicRender(store, workUnitStore)\n          return underlyingSearchParams[prop]\n        },\n        set(value) {\n          Object.defineProperty(promise, prop, {\n            value,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams,\n  store: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  // We have an unfortunate sequence of events that requires this initialization logic. We want to instrument the underlying\n  // searchParams object to detect if you are accessing values in dev. This is used for warnings and for things like the static prerender\n  // indicator. However when we pass this proxy to our Promise.resolve() below the VM checks if the resolved value is a promise by looking\n  // at the `.then` property. To our dynamic tracking logic this is indistinguishable from a `then` searchParam and so we would normally trigger\n  // dynamic tracking. However we know that this .then is not real dynamic access, it's just how thenables resolve in sequence. So we introduce\n  // this initialization concept so we omit the dynamic check until after we've constructed our resolved promise.\n  let promiseInitialized = false\n  const proxiedUnderlying = new Proxy(underlyingSearchParams, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string' && promiseInitialized) {\n        if (store.dynamicShouldError) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            store.route,\n            expression\n          )\n        }\n        const workUnitStore = workUnitAsyncStorage.getStore()\n        trackDynamicDataInDynamicRender(store, workUnitStore)\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (store.dynamicShouldError) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            store.route,\n            expression\n          )\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      if (store.dynamicShouldError) {\n        const expression =\n          '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          store.route,\n          expression\n        )\n      }\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = new Promise<SearchParams>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingSearchParams))\n  )\n  promise.then(() => {\n    promiseInitialized = true\n  })\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      Object.defineProperty(promise, prop, {\n        get() {\n          return proxiedUnderlying[prop]\n        },\n        set(newValue) {\n          Object.defineProperty(promise, prop, {\n            value: newValue,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (prop === 'then' && store.dynamicShouldError) {\n        const expression = '`searchParams.then`'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          store.route,\n          expression\n        )\n      }\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          syncIODev(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          syncIODev(store.route, expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      const expression = '`Object.keys(searchParams)` or similar'\n      syncIODev(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction syncIODev(\n  route: string | undefined,\n  expression: string,\n  missingProperties?: Array<string>\n) {\n  // In all cases we warn normally\n  if (missingProperties && missingProperties.length > 0) {\n    warnForIncompleteEnumeration(route, expression, missingProperties)\n  } else {\n    warnForSyncAccess(route, expression)\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createSearchAccessError\n)\n\nconst warnForIncompleteEnumeration =\n  createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError)\n\nfunction createSearchAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`searchParams\\` should be awaited before using its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction createIncompleteEnumerationError(\n  route: string | undefined,\n  expression: string,\n  missingProperties: Array<string>\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`searchParams\\` should be awaited before using its properties. ` +\n      `The following properties were not available through enumeration ` +\n      `because they conflict with builtin or well-known property names: ` +\n      `${describeListOfPropertyNames(missingProperties)}. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n"], "names": ["createPrerenderSearchParamsForClientPage", "createSearchParamsFromClient", "createServerSearchParamsForMetadata", "createServerSearchParamsForServerPage", "makeErroringExoticSearchParamsForUseCache", "underlyingSearchParams", "workStore", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "createPrerenderSearchParams", "createRenderSearchParams", "forceStatic", "Promise", "resolve", "prerenderStore", "makeHangingPromise", "renderSignal", "makeAbortingExoticSearchParams", "route", "makeErroringExoticSearchParams", "process", "env", "NODE_ENV", "isPrefetchRequest", "makeDynamicallyTrackedExoticSearchParamsWithDevWarnings", "makeUntrackedExoticSearchParams", "CachedSearchParams", "WeakMap", "CachedSearchParamsForUseCache", "cachedSearchParams", "get", "promise", "proxiedPromise", "Proxy", "target", "prop", "receiver", "Object", "hasOwn", "ReflectAdapter", "expression", "annotateDynamicAccess", "wellKnownProperties", "has", "describeStringPropertyAccess", "error", "createSearchAccessError", "abortAndThrowOnSynchronousRequestDataAccess", "describeHasCheckingStringProperty", "ownKeys", "set", "dynamicShouldError", "throwWithStaticGenerationBailoutErrorWithDynamicError", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "throwForSearchParamsAccessInUseCache", "store", "keys", "for<PERSON>ach", "defineProperty", "trackDynamicDataInDynamicRender", "value", "writable", "enumerable", "configurable", "proxiedProperties", "Set", "unproxiedProperties", "promiseInitialized", "proxiedUnderlying", "Reflect", "scheduleImmediate", "then", "push", "add", "newValue", "syncIODev", "delete", "missingProperties", "length", "warnForIncompleteEnumeration", "warnForSyncAccess", "prerenderPhase", "requestStore", "trackSynchronousRequestDataAccessInDev", "createDedupedByCallsiteServerErrorLoggerDev", "createIncompleteEnumerationError", "prefix", "Error", "describeListOfPropertyNames", "properties", "InvariantError", "description", "i"], "mappings": "AA4JMsB,QAAQC,GAAG,CAACC,QAAQ;;;;;;;;;;;;;;;;;;;IArDVxB,wCAAwC,EAAA;eAAxCA;;IAxCAC,4BAA4B,EAAA;eAA5BA;;IAmBHC,mCAAmC,EAAA;eAAnCA;;IAGGC,qCAAqC,EAAA;eAArCA;;IAmWAC,yCAAyC,EAAA;eAAzCA;;;yBAtbe;kCAQxB;8CAQA;gCACwB;uCACI;0DACyB;8BAKrD;uBAIA;2BAC2B;AAgC3B,SAASH,6BACdI,sBAAoC,EACpCC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;g<PERSON><PERSON>,OAAOC,4BAA4BL,WAAWC;YAChD;QAEF;IACF;IACA,OAAOK,yBAAyBP,wBAAwBC;AAC1D;AAGO,MAAMJ,sCACXC;AAEK,SAASA,sCACdE,sBAAoC,EACpCC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BAA4BL,WAAWC;YAChD;QAEF;IACF;IACA,OAAOK,yBAAyBP,wBAAwBC;AAC1D;AAEO,SAASN,yCACdM,SAAoB;IAEpB,IAAIA,UAAUO,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B;IAEA,MAAMC,iBAAiBR,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,IAAIO,kBAAkBA,eAAeN,IAAI,KAAK,aAAa;QACzD,sBAAsB;QACtB,wEAAwE;QACxE,2DAA2D;QAC3D,OAAOO,CAAAA,GAAAA,uBAAAA,kBAAkB,EAACD,eAAeE,YAAY,EAAE;IACzD;IACA,oFAAoF;IACpF,gGAAgG;IAChG,mBAAmB;IACnB,OAAOJ,QAAQC,OAAO,CAAC,CAAC;AAC1B;AAEA,SAASJ,4BACPL,SAAoB,EACpBU,cAA8B;IAE9B,IAAIV,UAAUO,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B;IAEA,IAAIC,eAAeN,IAAI,KAAK,aAAa;QACvC,qDAAqD;QACrD,OAAOS,+BAA+Bb,UAAUc,KAAK,EAAEJ;IACzD;IAEA,6DAA6D;IAC7D,2EAA2E;IAC3E,mCAAmC;IACnC,OAAOK,+BAA+Bf,WAAWU;AACnD;AAEA,SAASJ,yBACPP,sBAAoC,EACpCC,SAAoB;IAEpB,IAAIA,UAAUO,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B,OAAO;QACL,wDAC2B,iBACzB,CAACT,UAAUmB,iBAAiB,EAC5B;YACA,OAAOC,wDACLrB,wBACAC;QAEJ,OAAO;YACL,OAAOqB,gCAAgCtB,wBAAwBC;QACjE;IACF;AACF;AAGA,MAAMsB,qBAAqB,IAAIC;AAE/B,MAAMC,gCAAgC,IAAID;AAK1C,SAASV,+BACPC,KAAa,EACbJ,cAAoC;IAEpC,MAAMe,qBAAqBH,mBAAmBI,GAAG,CAAChB;IAClD,IAAIe,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUhB,CAAAA,GAAAA,uBAAAA,kBAAkB,EAChCD,eAAeE,YAAY,EAC3B;IAGF,MAAMgB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,6DAA6D;gBAC7D,qEAAqE;gBACrE,0FAA0F;gBAC1F,OAAOI,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,OAAQD;gBACN,KAAK;oBAAQ;wBACX,MAAMK,aACJ;wBACFC,CAAAA,GAAAA,kBAAAA,qBAAqB,EAACD,YAAY1B;wBAClC,OAAOyB,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;gBACA,KAAK;oBAAU;wBACb,MAAMI,aACJ;wBACFC,CAAAA,GAAAA,kBAAAA,qBAAqB,EAACD,YAAY1B;wBAClC,OAAOyB,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;gBAEA;oBAAS;wBACP,IAAI,OAAOD,SAAS,YAAY,CAACO,cAAAA,mBAAmB,CAACC,GAAG,CAACR,OAAO;4BAC9D,MAAMK,aAAaI,CAAAA,GAAAA,cAAAA,4BAA4B,EAC7C,gBACAT;4BAEF,MAAMU,QAAQC,wBAAwB5B,OAAOsB;4BAC7CO,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC7B,OACAsB,YACAK,OACA/B;wBAEJ;wBACA,OAAOyB,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;YACF;QACF;QACAO,KAAIT,MAAM,EAAEC,IAAI;YACd,8EAA8E;YAC9E,wFAAwF;YACxF,8FAA8F;YAC9F,kEAAkE;YAClE,IAAI,OAAOA,SAAS,UAAU;gBAC5B,MAAMK,aAAaQ,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAb;gBAEF,MAAMU,QAAQC,wBAAwB5B,OAAOsB;gBAC7CO,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC7B,OACAsB,YACAK,OACA/B;YAEJ;YACA,OAAOyB,SAAAA,cAAc,CAACI,GAAG,CAACT,QAAQC;QACpC;QACAc;YACE,MAAMT,aACJ;YACF,MAAMK,QAAQC,wBAAwB5B,OAAOsB;YAC7CO,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzC7B,OACAsB,YACAK,OACA/B;QAEJ;IACF;IAEAY,mBAAmBwB,GAAG,CAACpC,gBAAgBkB;IACvC,OAAOA;AACT;AAEA,SAASb,+BACPf,SAAoB,EACpBU,cAAwD;IAExD,MAAMe,qBAAqBH,mBAAmBI,GAAG,CAAC1B;IAClD,IAAIyB,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAM1B,yBAAyB,CAAC;IAChC,mFAAmF;IACnF,qFAAqF;IACrF,+DAA+D;IAC/D,MAAM4B,UAAUnB,QAAQC,OAAO,CAACV;IAEhC,MAAM6B,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,6DAA6D;gBAC7D,qEAAqE;gBACrE,0FAA0F;gBAC1F,OAAOI,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,OAAQD;gBACN,KAAK;oBAAQ;wBACX,MAAMK,aACJ;wBACF,IAAIpC,UAAU+C,kBAAkB,EAAE;4BAChCC,CAAAA,GAAAA,OAAAA,qDAAqD,EACnDhD,UAAUc,KAAK,EACfsB;wBAEJ,OAAO,IAAI1B,eAAeN,IAAI,KAAK,iBAAiB;4BAClD,+BAA+B;4BAC/B6C,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClBjD,UAAUc,KAAK,EACfsB,YACA1B,eAAewC,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9Bf,YACApC,WACAU;wBAEJ;wBACA;oBACF;gBACA,KAAK;oBAAU;wBACb,MAAM0B,aACJ;wBACF,IAAIpC,UAAU+C,kBAAkB,EAAE;4BAChCC,CAAAA,GAAAA,OAAAA,qDAAqD,EACnDhD,UAAUc,KAAK,EACfsB;wBAEJ,OAAO,IAAI1B,eAAeN,IAAI,KAAK,iBAAiB;4BAClD,+BAA+B;4BAC/B6C,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClBjD,UAAUc,KAAK,EACfsB,YACA1B,eAAewC,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9Bf,YACApC,WACAU;wBAEJ;wBACA;oBACF;gBACA;oBAAS;wBACP,IAAI,OAAOqB,SAAS,YAAY,CAACO,cAAAA,mBAAmB,CAACC,GAAG,CAACR,OAAO;4BAC9D,MAAMK,aAAaI,CAAAA,GAAAA,cAAAA,4BAA4B,EAC7C,gBACAT;4BAEF,IAAI/B,UAAU+C,kBAAkB,EAAE;gCAChCC,CAAAA,GAAAA,OAAAA,qDAAqD,EACnDhD,UAAUc,KAAK,EACfsB;4BAEJ,OAAO,IAAI1B,eAAeN,IAAI,KAAK,iBAAiB;gCAClD,+BAA+B;gCAC/B6C,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClBjD,UAAUc,KAAK,EACfsB,YACA1B,eAAewC,eAAe;4BAElC,OAAO;gCACL,mBAAmB;gCACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9Bf,YACApC,WACAU;4BAEJ;wBACF;wBACA,OAAOyB,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;YACF;QACF;QACAO,KAAIT,MAAM,EAAEC,IAAI;YACd,8EAA8E;YAC9E,wFAAwF;YACxF,8FAA8F;YAC9F,kEAAkE;YAClE,IAAI,OAAOA,SAAS,UAAU;gBAC5B,MAAMK,aAAaQ,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAb;gBAEF,IAAI/B,UAAU+C,kBAAkB,EAAE;oBAChCC,CAAAA,GAAAA,OAAAA,qDAAqD,EACnDhD,UAAUc,KAAK,EACfsB;gBAEJ,OAAO,IAAI1B,eAAeN,IAAI,KAAK,iBAAiB;oBAClD,+BAA+B;oBAC/B6C,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClBjD,UAAUc,KAAK,EACfsB,YACA1B,eAAewC,eAAe;gBAElC,OAAO;oBACL,mBAAmB;oBACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9Bf,YACApC,WACAU;gBAEJ;gBACA,OAAO;YACT;YACA,OAAOyB,SAAAA,cAAc,CAACI,GAAG,CAACT,QAAQC;QACpC;QACAc;YACE,MAAMT,aACJ;YACF,IAAIpC,UAAU+C,kBAAkB,EAAE;gBAChCC,CAAAA,GAAAA,OAAAA,qDAAqD,EACnDhD,UAAUc,KAAK,EACfsB;YAEJ,OAAO,IAAI1B,eAAeN,IAAI,KAAK,iBAAiB;gBAClD,+BAA+B;gBAC/B6C,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClBjD,UAAUc,KAAK,EACfsB,YACA1B,eAAewC,eAAe;YAElC,OAAO;gBACL,mBAAmB;gBACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAACf,YAAYpC,WAAWU;YAC1D;QACF;IACF;IAEAY,mBAAmBwB,GAAG,CAAC9C,WAAW4B;IAClC,OAAOA;AACT;AAOO,SAAS9B,0CACdE,SAAoB;IAEpB,MAAMyB,qBAAqBD,8BAA8BE,GAAG,CAAC1B;IAC7D,IAAIyB,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUnB,QAAQC,OAAO,CAAC,CAAC;IAEjC,MAAMmB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,wEAAwE;gBACxE,mEAAmE;gBACnE,+DAA+D;gBAC/D,oBAAoB;gBACpB,OAAOI,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,IACE,OAAOD,SAAS,YACfA,CAAAA,SAAS,UAAU,CAACO,cAAAA,mBAAmB,CAACC,GAAG,CAACR,KAAI,GACjD;gBACAqB,CAAAA,GAAAA,OAAAA,oCAAoC,EAACpD;YACvC;YAEA,OAAOmC,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAO,KAAIT,MAAM,EAAEC,IAAI;YACd,8EAA8E;YAC9E,uFAAuF;YACvF,8FAA8F;YAC9F,kEAAkE;YAClE,IACE,OAAOA,SAAS,YACfA,CAAAA,SAAS,UAAU,CAACO,cAAAA,mBAAmB,CAACC,GAAG,CAACR,KAAI,GACjD;gBACAqB,CAAAA,GAAAA,OAAAA,oCAAoC,EAACpD;YACvC;YAEA,OAAOmC,SAAAA,cAAc,CAACI,GAAG,CAACT,QAAQC;QACpC;QACAc;YACEO,CAAAA,GAAAA,OAAAA,oCAAoC,EAACpD;QACvC;IACF;IAEAwB,8BAA8BsB,GAAG,CAAC9C,WAAW4B;IAC7C,OAAOA;AACT;AAEA,SAASP,gCACPtB,sBAAoC,EACpCsD,KAAgB;IAEhB,MAAM5B,qBAAqBH,mBAAmBI,GAAG,CAAC3B;IAClD,IAAI0B,oBAAoB;QACtB,OAAOA;IACT;IAEA,kEAAkE;IAClE,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUnB,QAAQC,OAAO,CAACV;IAChCuB,mBAAmBwB,GAAG,CAAC/C,wBAAwB4B;IAE/CM,OAAOqB,IAAI,CAACvD,wBAAwBwD,OAAO,CAAC,CAACxB;QAC3C,IAAI,CAACO,cAAAA,mBAAmB,CAACC,GAAG,CAACR,OAAO;YAClCE,OAAOuB,cAAc,CAAC7B,SAASI,MAAM;gBACnCL;oBACE,MAAMzB,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;oBACnDsD,CAAAA,GAAAA,kBAAAA,+BAA+B,EAACJ,OAAOpD;oBACvC,OAAOF,sBAAsB,CAACgC,KAAK;gBACrC;gBACAe,KAAIY,KAAK;oBACPzB,OAAOuB,cAAc,CAAC7B,SAASI,MAAM;wBACnC2B;wBACAC,UAAU;wBACVC,YAAY;oBACd;gBACF;gBACAA,YAAY;gBACZC,cAAc;YAChB;QACF;IACF;IAEA,OAAOlC;AACT;AAEA,SAASP,wDACPrB,sBAAoC,EACpCsD,KAAgB;IAEhB,MAAM5B,qBAAqBH,mBAAmBI,GAAG,CAAC3B;IAClD,IAAI0B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAMqC,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7C,0HAA0H;IAC1H,uIAAuI;IACvI,wIAAwI;IACxI,8IAA8I;IAC9I,6IAA6I;IAC7I,+GAA+G;IAC/G,IAAIC,qBAAqB;IACzB,MAAMC,oBAAoB,IAAIrC,MAAM9B,wBAAwB;QAC1D2B,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,YAAYkC,oBAAoB;gBAClD,IAAIZ,MAAMN,kBAAkB,EAAE;oBAC5B,MAAMX,aAAaI,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,gBAAgBT;oBAChEiB,CAAAA,GAAAA,OAAAA,qDAAqD,EACnDK,MAAMvC,KAAK,EACXsB;gBAEJ;gBACA,MAAMnC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;gBACnDsD,CAAAA,GAAAA,kBAAAA,+BAA+B,EAACJ,OAAOpD;YACzC;YACA,OAAOkC,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAO,KAAIT,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IAAIsB,MAAMN,kBAAkB,EAAE;oBAC5B,MAAMX,aAAaQ,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAb;oBAEFiB,CAAAA,GAAAA,OAAAA,qDAAqD,EACnDK,MAAMvC,KAAK,EACXsB;gBAEJ;YACF;YACA,OAAO+B,QAAQ5B,GAAG,CAACT,QAAQC;QAC7B;QACAc,SAAQf,MAAM;YACZ,IAAIuB,MAAMN,kBAAkB,EAAE;gBAC5B,MAAMX,aACJ;gBACFY,CAAAA,GAAAA,OAAAA,qDAAqD,EACnDK,MAAMvC,KAAK,EACXsB;YAEJ;YACA,OAAO+B,QAAQtB,OAAO,CAACf;QACzB;IACF;IAEA,kEAAkE;IAClE,kEAAkE;IAClE,qEAAqE;IACrE,MAAMH,UAAU,IAAInB,QAAsB,CAACC,UACzC2D,CAAAA,GAAAA,WAAAA,iBAAiB,EAAC,IAAM3D,QAAQV;IAElC4B,QAAQ0C,IAAI,CAAC;QACXJ,qBAAqB;IACvB;IAEAhC,OAAOqB,IAAI,CAACvD,wBAAwBwD,OAAO,CAAC,CAACxB;QAC3C,IAAIO,cAAAA,mBAAmB,CAACC,GAAG,CAACR,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEiC,oBAAoBM,IAAI,CAACvC;QAC3B,OAAO;YACL+B,kBAAkBS,GAAG,CAACxC;YACtBE,OAAOuB,cAAc,CAAC7B,SAASI,MAAM;gBACnCL;oBACE,OAAOwC,iBAAiB,CAACnC,KAAK;gBAChC;gBACAe,KAAI0B,QAAQ;oBACVvC,OAAOuB,cAAc,CAAC7B,SAASI,MAAM;wBACnC2B,OAAOc;wBACPb,UAAU;wBACVC,YAAY;oBACd;gBACF;gBACAA,YAAY;gBACZC,cAAc;YAChB;QACF;IACF;IAEA,MAAMjC,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAID,SAAS,UAAUsB,MAAMN,kBAAkB,EAAE;gBAC/C,MAAMX,aAAa;gBACnBY,CAAAA,GAAAA,OAAAA,qDAAqD,EACnDK,MAAMvC,KAAK,EACXsB;YAEJ;YACA,IAAI,OAAOL,SAAS,UAAU;gBAC5B,IACE,CAACO,cAAAA,mBAAmB,CAACC,GAAG,CAACR,SACxB+B,CAAAA,kBAAkBvB,GAAG,CAACR,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BoC,QAAQ5B,GAAG,CAACT,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMK,aAAaI,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,gBAAgBT;oBAChE0C,UAAUpB,MAAMvC,KAAK,EAAEsB;gBACzB;YACF;YACA,OAAOD,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAc,KAAIhB,MAAM,EAAEC,IAAI,EAAE2B,KAAK,EAAE1B,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5B+B,kBAAkBY,MAAM,CAAC3C;YAC3B;YACA,OAAOoC,QAAQrB,GAAG,CAAChB,QAAQC,MAAM2B,OAAO1B;QAC1C;QACAO,KAAIT,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,CAACO,cAAAA,mBAAmB,CAACC,GAAG,CAACR,SACxB+B,CAAAA,kBAAkBvB,GAAG,CAACR,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BoC,QAAQ5B,GAAG,CAACT,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMK,aAAaQ,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAb;oBAEF0C,UAAUpB,MAAMvC,KAAK,EAAEsB;gBACzB;YACF;YACA,OAAO+B,QAAQ5B,GAAG,CAACT,QAAQC;QAC7B;QACAc,SAAQf,MAAM;YACZ,MAAMM,aAAa;YACnBqC,UAAUpB,MAAMvC,KAAK,EAAEsB,YAAY4B;YACnC,OAAOG,QAAQtB,OAAO,CAACf;QACzB;IACF;IAEAR,mBAAmBwB,GAAG,CAAC/C,wBAAwB6B;IAC/C,OAAOA;AACT;AAEA,SAAS6C,UACP3D,KAAyB,EACzBsB,UAAkB,EAClBuC,iBAAiC;IAEjC,gCAAgC;IAChC,IAAIA,qBAAqBA,kBAAkBC,MAAM,GAAG,GAAG;QACrDC,6BAA6B/D,OAAOsB,YAAYuC;IAClD,OAAO;QACLG,kBAAkBhE,OAAOsB;IAC3B;IAEA,MAAMnC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IACEF,iBACAA,cAAcG,IAAI,KAAK,aACvBH,cAAc8E,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAMC,eAAe/E;QACrBgF,CAAAA,GAAAA,kBAAAA,sCAAsC,EAACD;IACzC;AACF;AAEA,MAAMF,oBAAoBI,CAAAA,GAAAA,0CAAAA,2CAA2C,EACnExC;AAGF,MAAMmC,+BACJK,CAAAA,GAAAA,0CAAAA,2CAA2C,EAACC;AAE9C,SAASzC,wBACP5B,KAAyB,EACzBsB,UAAkB;IAElB,MAAMgD,SAAStE,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAIuE,MACT,GAAGD,OAAO,KAAK,EAAEhD,WAAW,EAAE,CAAC,GAC7B,CAAC,gEAAgE,CAAC,GAClE,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAAS+C,iCACPrE,KAAyB,EACzBsB,UAAkB,EAClBuC,iBAAgC;IAEhC,MAAMS,SAAStE,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAON,CAPM,IAAIuE,MACT,GAAGD,OAAO,KAAK,EAAEhD,WAAW,EAAE,CAAC,GAC7B,CAAC,gEAAgE,CAAC,GAClE,CAAC,gEAAgE,CAAC,GAClE,CAAC,iEAAiE,CAAC,GACnE,GAAGkD,4BAA4BX,mBAAmB,EAAE,CAAC,GACrD,CAAC,8DAA8D,CAAC,GAN7D,qBAAA;eAAA;oBAAA;sBAAA;IAOP;AACF;AAEA,SAASW,4BAA4BC,UAAyB;IAC5D,OAAQA,WAAWX,MAAM;QACvB,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,IAAIY,gBAAAA,cAAc,CACtB,wFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YACH,OAAO,CAAC,EAAE,EAAED,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QAC/B,KAAK;YACH,OAAO,CAAC,EAAE,EAAEA,UAAU,CAAC,EAAE,CAAC,SAAS,EAAEA,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QACxD;YAAS;gBACP,IAAIE,cAAc;gBAClB,IAAK,IAAIC,IAAI,GAAGA,IAAIH,WAAWX,MAAM,GAAG,GAAGc,IAAK;oBAC9CD,eAAe,CAAC,EAAE,EAAEF,UAAU,CAACG,EAAE,CAAC,IAAI,CAAC;gBACzC;gBACAD,eAAe,CAAC,QAAQ,EAAEF,UAAU,CAACA,WAAWX,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC;gBAC/D,OAAOa;YACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1648, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/server/request/params.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\nimport type { FallbackRouteParams } from './fallback-params'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n  type PrerenderStorePPR,\n  type PrerenderStoreLegacy,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { scheduleImmediate } from '../../lib/scheduler'\n\nexport type ParamValue = string | Array<string> | undefined\nexport type Params = Record<string, ParamValue>\n\n/**\n * In this version of Next.js the `params` prop passed to Layouts, Pages, and other Segments is a Promise.\n * However to facilitate migration to this new Promise type you can currently still access params directly on the Promise instance passed to these Segments.\n * The `UnsafeUnwrappedParams` type is available if you need to temporarily access the underlying params without first awaiting or `use`ing the Promise.\n *\n * In a future version of Next.js the `params` prop will be a plain Promise and this type will be removed.\n *\n * Typically instances of `params` can be updated automatically to be treated as a Promise by a codemod published alongside this Next.js version however if you\n * have not yet run the codemod of the codemod cannot detect certain instances of `params` usage you should first try to refactor your code to await `params`.\n *\n * If refactoring is not possible but you still want to be able to access params directly without typescript errors you can cast the params Promise to this type\n *\n * ```tsx\n * type Props = { params: Promise<{ id: string }>}\n *\n * export default async function Layout(props: Props) {\n *  const directParams = (props.params as unknown as UnsafeUnwrappedParams<typeof props.params>)\n *  return ...\n * }\n * ```\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedParams<P> =\n  P extends Promise<infer U> ? Omit<U, 'then' | 'status' | 'value'> : never\n\nexport function createParamsFromClient(\n  underlyingParams: Params,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport type CreateServerParamsForMetadata = typeof createServerParamsForMetadata\nexport const createServerParamsForMetadata = createServerParamsForServerSegment\n\n// routes always runs in RSC context so it is equivalent to a Server Page Component\nexport function createServerParamsForRoute(\n  underlyingParams: Params,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\nexport function createServerParamsForServerSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\nexport function createPrerenderParamsForClientSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (prerenderStore && prerenderStore.type === 'prerender') {\n    const fallbackParams = workStore.fallbackRouteParams\n    if (fallbackParams) {\n      for (let key in underlyingParams) {\n        if (fallbackParams.has(key)) {\n          // This params object has one of more fallback params so we need to consider\n          // the awaiting of this params object \"dynamic\". Since we are in dynamicIO mode\n          // we encode this as a promise that never resolves\n          return makeHangingPromise(prerenderStore.renderSignal, '`params`')\n        }\n      }\n    }\n  }\n  // We're prerendering in a mode that does not abort. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve(underlyingParams)\n}\n\nfunction createPrerenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStore\n): Promise<Params> {\n  const fallbackParams = workStore.fallbackRouteParams\n  if (fallbackParams) {\n    let hasSomeFallbackParams = false\n    for (const key in underlyingParams) {\n      if (fallbackParams.has(key)) {\n        hasSomeFallbackParams = true\n        break\n      }\n    }\n\n    if (hasSomeFallbackParams) {\n      // params need to be treated as dynamic because we have at least one fallback param\n      if (prerenderStore.type === 'prerender') {\n        // We are in a dynamicIO (PPR or otherwise) prerender\n        return makeAbortingExoticParams(\n          underlyingParams,\n          workStore.route,\n          prerenderStore\n        )\n      }\n      // remaining cases are prerender-ppr and prerender-legacy\n      // We aren't in a dynamicIO prerender but we do have fallback params at this\n      // level so we need to make an erroring exotic params object which will postpone\n      // if you access the fallback params\n      return makeErroringExoticParams(\n        underlyingParams,\n        fallbackParams,\n        workStore,\n        prerenderStore\n      )\n    }\n  }\n\n  // We don't have any fallback params so we have an entirely static safe params object\n  return makeUntrackedExoticParams(underlyingParams)\n}\n\nfunction createRenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  if (process.env.NODE_ENV === 'development' && !workStore.isPrefetchRequest) {\n    return makeDynamicallyTrackedExoticParamsWithDevWarnings(\n      underlyingParams,\n      workStore\n    )\n  } else {\n    return makeUntrackedExoticParams(underlyingParams)\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nfunction makeAbortingExoticParams(\n  underlyingParams: Params,\n  route: string,\n  prerenderStore: PrerenderStoreModern\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = makeHangingPromise<Params>(\n    prerenderStore.renderSignal,\n    '`params`'\n  )\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      Object.defineProperty(promise, prop, {\n        get() {\n          const expression = describeStringPropertyAccess('params', prop)\n          const error = createParamsAccessError(route, expression)\n          abortAndThrowOnSynchronousRequestDataAccess(\n            route,\n            expression,\n            error,\n            prerenderStore\n          )\n        },\n        set(newValue) {\n          Object.defineProperty(promise, prop, {\n            value: newValue,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  return promise\n}\n\nfunction makeErroringExoticParams(\n  underlyingParams: Params,\n  fallbackParams: FallbackRouteParams,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStorePPR | PrerenderStoreLegacy\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const augmentedUnderlying = { ...underlyingParams }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(augmentedUnderlying)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      if (fallbackParams.has(prop)) {\n        Object.defineProperty(augmentedUnderlying, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when dynamicIO is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          enumerable: true,\n        })\n        Object.defineProperty(promise, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when dynamicIO is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          set(newValue) {\n            Object.defineProperty(promise, prop, {\n              value: newValue,\n              writable: true,\n              enumerable: true,\n            })\n          },\n          enumerable: true,\n          configurable: true,\n        })\n      } else {\n        ;(promise as any)[prop] = underlyingParams[prop]\n      }\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedExoticParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedExoticParamsWithDevWarnings(\n  underlyingParams: Params,\n  store: WorkStore\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = new Promise<Params>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingParams))\n  )\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          syncIODev(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      const expression = '`...params` or similar expression'\n      syncIODev(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction syncIODev(\n  route: string | undefined,\n  expression: string,\n  missingProperties?: Array<string>\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  if (missingProperties && missingProperties.length > 0) {\n    warnForIncompleteEnumeration(route, expression, missingProperties)\n  } else {\n    warnForSyncAccess(route, expression)\n  }\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createParamsAccessError\n)\n\nconst warnForIncompleteEnumeration =\n  createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError)\n\nfunction createParamsAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction createIncompleteEnumerationError(\n  route: string | undefined,\n  expression: string,\n  missingProperties: Array<string>\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `The following properties were not available through enumeration ` +\n      `because they conflict with builtin property names: ` +\n      `${describeListOfPropertyNames(missingProperties)}. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n"], "names": ["createParamsFromClient", "createPrerenderParamsForClientSegment", "createServerParamsForMetadata", "createServerParamsForRoute", "createServerParamsForServerSegment", "underlyingParams", "workStore", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "createPrerenderParams", "createRenderParams", "prerenderStore", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "key", "has", "makeHangingPromise", "renderSignal", "Promise", "resolve", "hasSomeFallbackParams", "makeAbortingExoticParams", "route", "makeErroringExoticParams", "makeUntrackedExoticParams", "process", "env", "NODE_ENV", "isPrefetchRequest", "makeDynamicallyTrackedExoticParamsWithDevWarnings", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "cachedParams", "get", "promise", "set", "Object", "keys", "for<PERSON>ach", "prop", "wellKnownProperties", "defineProperty", "expression", "describeStringPropertyAccess", "error", "createParamsAccessError", "abortAndThrowOnSynchronousRequestDataAccess", "newValue", "value", "writable", "enumerable", "configurable", "augmentedUnderlying", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "store", "scheduleImmediate", "proxiedProperties", "Set", "unproxiedProperties", "push", "add", "proxiedPromise", "Proxy", "target", "receiver", "syncIODev", "ReflectAdapter", "delete", "ownKeys", "Reflect", "missingProperties", "prerenderPhase", "requestStore", "trackSynchronousRequestDataAccessInDev", "length", "warnForIncompleteEnumeration", "warnForSyncAccess", "createDedupedByCallsiteServerErrorLoggerDev", "createIncompleteEnumerationError", "prefix", "Error", "describeListOfPropertyNames", "properties", "InvariantError", "description", "i"], "mappings": "AA2LM2B,QAAQC,GAAG,CAACC,QAAQ;;;;;;;;;;;;;;;;;;;IAjIV7B,sBAAsB,EAAA;eAAtBA;;IA2DAC,qCAAqC,EAAA;eAArCA;;IAvCHC,6BAA6B,EAAA;eAA7BA;;IAGGC,0BAA0B,EAAA;eAA1BA;;IAkBAC,kCAAkC,EAAA;eAAlCA;;;yBAhGe;kCAMxB;8CAQA;gCACwB;8BAIxB;uCAC4B;0DACyB;2BAC1B;AAiC3B,SAASJ,uBACdK,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;gBA<PERSON>,OAAOC,sBAAsBN,kBAAkBC,WAAWC;YAC5D;QAEF;IACF;IACA,OAAOK,mBAAmBP,kBAAkBC;AAC9C;AAIO,MAAMJ,gCAAgCE;AAGtC,SAASD,2BACdE,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,sBAAsBN,kBAAkBC,WAAWC;YAC5D;QAEF;IACF;IACA,OAAOK,mBAAmBP,kBAAkBC;AAC9C;AAEO,SAASF,mCACdC,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,sBAAsBN,kBAAkBC,WAAWC;YAC5D;QAEF;IACF;IACA,OAAOK,mBAAmBP,kBAAkBC;AAC9C;AAEO,SAASL,sCACdI,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMO,iBAAiBL,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,IAAII,kBAAkBA,eAAeH,IAAI,KAAK,aAAa;QACzD,MAAMI,iBAAiBR,UAAUS,mBAAmB;QACpD,IAAID,gBAAgB;YAClB,IAAK,IAAIE,OAAOX,iBAAkB;gBAChC,IAAIS,eAAeG,GAAG,CAACD,MAAM;oBAC3B,4EAA4E;oBAC5E,+EAA+E;oBAC/E,kDAAkD;oBAClD,OAAOE,CAAAA,GAAAA,uBAAAA,kBAAkB,EAACL,eAAeM,YAAY,EAAE;gBACzD;YACF;QACF;IACF;IACA,mFAAmF;IACnF,gGAAgG;IAChG,mBAAmB;IACnB,OAAOC,QAAQC,OAAO,CAAChB;AACzB;AAEA,SAASM,sBACPN,gBAAwB,EACxBC,SAAoB,EACpBO,cAA8B;IAE9B,MAAMC,iBAAiBR,UAAUS,mBAAmB;IACpD,IAAID,gBAAgB;QAClB,IAAIQ,wBAAwB;QAC5B,IAAK,MAAMN,OAAOX,iBAAkB;YAClC,IAAIS,eAAeG,GAAG,CAACD,MAAM;gBAC3BM,wBAAwB;gBACxB;YACF;QACF;QAEA,IAAIA,uBAAuB;YACzB,mFAAmF;YACnF,IAAIT,eAAeH,IAAI,KAAK,aAAa;gBACvC,qDAAqD;gBACrD,OAAOa,yBACLlB,kBACAC,UAAUkB,KAAK,EACfX;YAEJ;YACA,yDAAyD;YACzD,4EAA4E;YAC5E,gFAAgF;YAChF,oCAAoC;YACpC,OAAOY,yBACLpB,kBACAS,gBACAR,WACAO;QAEJ;IACF;IAEA,qFAAqF;IACrF,OAAOa,0BAA0BrB;AACnC;AAEA,SAASO,mBACPP,gBAAwB,EACxBC,SAAoB;IAEpB,wDAA6B,iBAAiB,CAACA,UAAUwB,iBAAiB,EAAE;QAC1E,OAAOC,kDACL1B,kBACAC;IAEJ,OAAO;QACL,OAAOoB,0BAA0BrB;IACnC;AACF;AAGA,MAAM2B,eAAe,IAAIC;AAEzB,SAASV,yBACPlB,gBAAwB,EACxBmB,KAAa,EACbX,cAAoC;IAEpC,MAAMqB,eAAeF,aAAaG,GAAG,CAAC9B;IACtC,IAAI6B,cAAc;QAChB,OAAOA;IACT;IAEA,MAAME,UAAUlB,CAAAA,GAAAA,uBAAAA,kBAAkB,EAChCL,eAAeM,YAAY,EAC3B;IAEFa,aAAaK,GAAG,CAAChC,kBAAkB+B;IAEnCE,OAAOC,IAAI,CAAClC,kBAAkBmC,OAAO,CAAC,CAACC;QACrC,IAAIC,cAAAA,mBAAmB,CAACzB,GAAG,CAACwB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACLH,OAAOK,cAAc,CAACP,SAASK,MAAM;gBACnCN;oBACE,MAAMS,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUJ;oBAC1D,MAAMK,QAAQC,wBAAwBvB,OAAOoB;oBAC7CI,CAAAA,GAAAA,kBAAAA,2CAA2C,EACzCxB,OACAoB,YACAE,OACAjC;gBAEJ;gBACAwB,KAAIY,QAAQ;oBACVX,OAAOK,cAAc,CAACP,SAASK,MAAM;wBACnCS,OAAOD;wBACPE,UAAU;wBACVC,YAAY;oBACd;gBACF;gBACAA,YAAY;gBACZC,cAAc;YAChB;QACF;IACF;IAEA,OAAOjB;AACT;AAEA,SAASX,yBACPpB,gBAAwB,EACxBS,cAAmC,EACnCR,SAAoB,EACpBO,cAAwD;IAExD,MAAMqB,eAAeF,aAAaG,GAAG,CAAC9B;IACtC,IAAI6B,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMoB,sBAAsB;QAAE,GAAGjD,gBAAgB;IAAC;IAElD,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAM+B,UAAUhB,QAAQC,OAAO,CAACiC;IAChCtB,aAAaK,GAAG,CAAChC,kBAAkB+B;IAEnCE,OAAOC,IAAI,CAAClC,kBAAkBmC,OAAO,CAAC,CAACC;QACrC,IAAIC,cAAAA,mBAAmB,CAACzB,GAAG,CAACwB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACL,IAAI3B,eAAeG,GAAG,CAACwB,OAAO;gBAC5BH,OAAOK,cAAc,CAACW,qBAAqBb,MAAM;oBAC/CN;wBACE,MAAMS,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUJ;wBAC1D,oEAAoE;wBACpE,oEAAoE;wBACpE,wEAAwE;wBACxE,kBAAkB;wBAClB,qEAAqE;wBACrE,iCAAiC;wBACjC,IAAI5B,eAAeH,IAAI,KAAK,iBAAiB;4BAC3C,+BAA+B;4BAC/B6C,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClBjD,UAAUkB,KAAK,EACfoB,YACA/B,eAAe2C,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9Bb,YACAtC,WACAO;wBAEJ;oBACF;oBACAuC,YAAY;gBACd;gBACAd,OAAOK,cAAc,CAACP,SAASK,MAAM;oBACnCN;wBACE,MAAMS,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUJ;wBAC1D,oEAAoE;wBACpE,oEAAoE;wBACpE,wEAAwE;wBACxE,kBAAkB;wBAClB,qEAAqE;wBACrE,iCAAiC;wBACjC,IAAI5B,eAAeH,IAAI,KAAK,iBAAiB;4BAC3C,+BAA+B;4BAC/B6C,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClBjD,UAAUkB,KAAK,EACfoB,YACA/B,eAAe2C,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9Bb,YACAtC,WACAO;wBAEJ;oBACF;oBACAwB,KAAIY,QAAQ;wBACVX,OAAOK,cAAc,CAACP,SAASK,MAAM;4BACnCS,OAAOD;4BACPE,UAAU;4BACVC,YAAY;wBACd;oBACF;oBACAA,YAAY;oBACZC,cAAc;gBAChB;YACF,OAAO;;gBACHjB,OAAe,CAACK,KAAK,GAAGpC,gBAAgB,CAACoC,KAAK;YAClD;QACF;IACF;IAEA,OAAOL;AACT;AAEA,SAASV,0BAA0BrB,gBAAwB;IACzD,MAAM6B,eAAeF,aAAaG,GAAG,CAAC9B;IACtC,IAAI6B,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUhB,QAAQC,OAAO,CAAChB;IAChC2B,aAAaK,GAAG,CAAChC,kBAAkB+B;IAEnCE,OAAOC,IAAI,CAAClC,kBAAkBmC,OAAO,CAAC,CAACC;QACrC,IAAIC,cAAAA,mBAAmB,CAACzB,GAAG,CAACwB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;;YACHL,OAAe,CAACK,KAAK,GAAGpC,gBAAgB,CAACoC,KAAK;QAClD;IACF;IAEA,OAAOL;AACT;AAEA,SAASL,kDACP1B,gBAAwB,EACxBqD,KAAgB;IAEhB,MAAMxB,eAAeF,aAAaG,GAAG,CAAC9B;IACtC,IAAI6B,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAU,IAAIhB,QAAgB,CAACC,UACnCsC,CAAAA,GAAAA,WAAAA,iBAAiB,EAAC,IAAMtC,QAAQhB;IAGlC,MAAMuD,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7CxB,OAAOC,IAAI,CAAClC,kBAAkBmC,OAAO,CAAC,CAACC;QACrC,IAAIC,cAAAA,mBAAmB,CAACzB,GAAG,CAACwB,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEqB,oBAAoBC,IAAI,CAACtB;QAC3B,OAAO;YACLmB,kBAAkBI,GAAG,CAACvB;YACpBL,OAAe,CAACK,KAAK,GAAGpC,gBAAgB,CAACoC,KAAK;QAClD;IACF;IAEA,MAAMwB,iBAAiB,IAAIC,MAAM9B,SAAS;QACxCD,KAAIgC,MAAM,EAAE1B,IAAI,EAAE2B,QAAQ;YACxB,IAAI,OAAO3B,SAAS,UAAU;gBAC5B,IACE,AACAmB,kBAAkB3C,GAAG,CAACwB,OACtB,0CAFuE;oBAGvE,MAAMG,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUJ;oBAC1D4B,UAAUX,MAAMlC,KAAK,EAAEoB;gBACzB;YACF;YACA,OAAO0B,SAAAA,cAAc,CAACnC,GAAG,CAACgC,QAAQ1B,MAAM2B;QAC1C;QACA/B,KAAI8B,MAAM,EAAE1B,IAAI,EAAES,KAAK,EAAEkB,QAAQ;YAC/B,IAAI,OAAO3B,SAAS,UAAU;gBAC5BmB,kBAAkBW,MAAM,CAAC9B;YAC3B;YACA,OAAO6B,SAAAA,cAAc,CAACjC,GAAG,CAAC8B,QAAQ1B,MAAMS,OAAOkB;QACjD;QACAI,SAAQL,MAAM;YACZ,MAAMvB,aAAa;YACnByB,UAAUX,MAAMlC,KAAK,EAAEoB,YAAYkB;YACnC,OAAOW,QAAQD,OAAO,CAACL;QACzB;IACF;IAEAnC,aAAaK,GAAG,CAAChC,kBAAkB4D;IACnC,OAAOA;AACT;AAEA,SAASI,UACP7C,KAAyB,EACzBoB,UAAkB,EAClB8B,iBAAiC;IAEjC,MAAMnE,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IACEF,iBACAA,cAAcG,IAAI,KAAK,aACvBH,cAAcoE,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAMC,eAAerE;QACrBsE,CAAAA,GAAAA,kBAAAA,sCAAsC,EAACD;IACzC;IACA,gCAAgC;IAChC,IAAIF,qBAAqBA,kBAAkBI,MAAM,GAAG,GAAG;QACrDC,6BAA6BvD,OAAOoB,YAAY8B;IAClD,OAAO;QACLM,kBAAkBxD,OAAOoB;IAC3B;AACF;AAEA,MAAMoC,oBAAoBC,CAAAA,GAAAA,0CAAAA,2CAA2C,EACnElC;AAGF,MAAMgC,+BACJE,CAAAA,GAAAA,0CAAAA,2CAA2C,EAACC;AAE9C,SAASnC,wBACPvB,KAAyB,EACzBoB,UAAkB;IAElB,MAAMuC,SAAS3D,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAI4D,MACT,GAAGD,OAAO,KAAK,EAAEvC,WAAW,EAAE,CAAC,GAC7B,CAAC,0DAA0D,CAAC,GAC5D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAASsC,iCACP1D,KAAyB,EACzBoB,UAAkB,EAClB8B,iBAAgC;IAEhC,MAAMS,SAAS3D,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAON,CAPM,IAAI4D,MACT,GAAGD,OAAO,KAAK,EAAEvC,WAAW,EAAE,CAAC,GAC7B,CAAC,0DAA0D,CAAC,GAC5D,CAAC,gEAAgE,CAAC,GAClE,CAAC,mDAAmD,CAAC,GACrD,GAAGyC,4BAA4BX,mBAAmB,EAAE,CAAC,GACrD,CAAC,8DAA8D,CAAC,GAN7D,qBAAA;eAAA;oBAAA;sBAAA;IAOP;AACF;AAEA,SAASW,4BAA4BC,UAAyB;IAC5D,OAAQA,WAAWR,MAAM;QACvB,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,IAAIS,gBAAAA,cAAc,CACtB,wFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YACH,OAAO,CAAC,EAAE,EAAED,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QAC/B,KAAK;YACH,OAAO,CAAC,EAAE,EAAEA,UAAU,CAAC,EAAE,CAAC,SAAS,EAAEA,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QACxD;YAAS;gBACP,IAAIE,cAAc;gBAClB,IAAK,IAAIC,IAAI,GAAGA,IAAIH,WAAWR,MAAM,GAAG,GAAGW,IAAK;oBAC9CD,eAAe,CAAC,EAAE,EAAEF,UAAU,CAACG,EAAE,CAAC,IAAI,CAAC;gBACzC;gBACAD,eAAe,CAAC,QAAQ,EAAEF,UAAU,CAACA,WAAWR,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC;gBAC/D,OAAOU;YACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2018, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/client/components/client-page.tsx"], "sourcesContent": ["'use client'\n\nimport type { ParsedUrlQuery } from 'querystring'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params and searchParams to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Page component.\n *\n * additionally we may send promises representing the params and searchParams. We don't ever use these passed\n * values but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations.\n * It is up to the caller to decide if the promises are needed.\n */\nexport function ClientPageRoot({\n  Component,\n  searchParams,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promises,\n}: {\n  Component: React.ComponentType<any>\n  searchParams: ParsedUrlQuery\n  params: Params\n  promises?: Array<Promise<any>>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientSearchParams: Promise<ParsedUrlQuery>\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling searchParams in a client Page.'\n      )\n    }\n\n    const { createSearchParamsFromClient } =\n      require('../../server/request/search-params') as typeof import('../../server/request/search-params')\n    clientSearchParams = createSearchParamsFromClient(searchParams, store)\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  } else {\n    const { createRenderSearchParamsFromClient } =\n      require('../request/search-params.browser') as typeof import('../request/search-params.browser')\n    const clientSearchParams = createRenderSearchParamsFromClient(searchParams)\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  }\n}\n"], "names": ["ClientPageRoot", "Component", "searchParams", "params", "promises", "window", "workAsyncStorage", "require", "clientSearchParams", "clientParams", "store", "getStore", "InvariantError", "createSearchParamsFromClient", "createParamsFromClient", "createRenderSearchParamsFromClient", "createRenderParamsFromClient"], "mappings": "AAAA;;;;;+<PERSON>eg<PERSON>,kBAAAA;;;eAAAA;;;;gCAZe;AAYxB,SAASA,eAAe,KAW9B;IAX8B,IAAA,EAC7BC,SAAS,EACTC,YAAY,EACZC,MAAM,EACN,AACAC,QAAQ,EAMT,GAX8B,gDAIgC;IAQ7D,IAAI,OAAOC,WAAW,aAAa;QACjC,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QAEV,IAAIC;QACJ,IAAIC;QACJ,yEAAyE;QACzE,wEAAwE;QACxE,MAAMC,QAAQJ,iBAAiBK,QAAQ;QACvC,IAAI,CAACD,OAAO;YACV,MAAM,OAAA,cAEL,CAFK,IAAIE,gBAAAA,cAAc,CACtB,6EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM,EAAEC,4BAA4B,EAAE,GACpCN,QAAQ;QACVC,qBAAqBK,6BAA6BX,cAAcQ;QAEhE,MAAM,EAAEI,sBAAsB,EAAE,GAC9BP,QAAQ;QACVE,eAAeK,uBAAuBX,QAAQO;QAE9C,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACT,WAAAA;YAAUE,QAAQM;YAAcP,cAAcM;;IACxD,OAAO;QACL,MAAM,EAAEO,kCAAkC,EAAE,GAC1CR,QAAQ;QACV,MAAMC,qBAAqBO,mCAAmCb;QAC9D,MAAM,EAAEc,4BAA4B,EAAE,GACpCT,QAAQ;QACV,MAAME,eAAeO,6BAA6Bb;QAElD,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACF,WAAAA;YAAUE,QAAQM;YAAcP,cAAcM;;IACxD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2078, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/client/components/client-segment.tsx"], "sourcesContent": ["'use client'\n\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Segment component.\n *\n * additionally we may send a promise representing params. We don't ever use this passed\n * value but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations\n * such as when dynamicIO is enabled. It is up to the caller to decide if the promises are needed.\n */\nexport function ClientSegmentRoot({\n  Component,\n  slots,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promise,\n}: {\n  Component: React.ComponentType<any>\n  slots: { [key: string]: React.ReactNode }\n  params: Params\n  promise?: Promise<any>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling params in a client segment such as a Layout or Template.'\n      )\n    }\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component {...slots} params={clientParams} />\n  } else {\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n    return <Component {...slots} params={clientParams} />\n  }\n}\n"], "names": ["ClientSegmentRoot", "Component", "slots", "params", "promise", "window", "workAsyncStorage", "require", "clientParams", "store", "getStore", "InvariantError", "createParamsFromClient", "createRenderParamsFromClient"], "mappings": "AAAA;;;;;+BAcg<PERSON>,qBAAAA;;;eAAAA;;;;gCAZe;AAYxB,SAASA,kBAAkB,KAWjC;IAXiC,IAAA,EAChCC,SAAS,EACTC,KAAK,EACLC,MAAM,EACN,AACAC,OAAO,EAMR,GAXiC,iDAI6B;IAQ7D,IAAI,OAAOC,WAAW,aAAa;QACjC,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QAEV,IAAIC;QACJ,yEAAyE;QACzE,wEAAwE;QACxE,MAAMC,QAAQH,iBAAiBI,QAAQ;QACvC,IAAI,CAACD,OAAO;YACV,MAAM,OAAA,cAEL,CAFK,IAAIE,gBAAAA,cAAc,CACtB,uGADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM,EAAEC,sBAAsB,EAAE,GAC9BL,QAAQ;QACVC,eAAeI,uBAAuBT,QAAQM;QAE9C,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACR,WAAAA;YAAW,GAAGC,KAAK;YAAEC,QAAQK;;IACvC,OAAO;QACL,MAAM,EAAEK,4BAA4B,EAAE,GACpCN,QAAQ;QACV,MAAMC,eAAeK,6BAA6BV;QAClD,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACF,WAAAA;YAAW,GAAGC,KAAK;YAAEC,QAAQK;;IACvC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/client/components/metadata/browser-resolved-metadata.tsx"], "sourcesContent": ["import { use } from 'react'\nimport type { StreamingMetadataResolvedState } from './types'\n\nexport function BrowserResolvedMetadata({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  const { metadata, error } = use(promise)\n  // If there's metadata error on client, discard the browser metadata\n  // and let metadata outlet deal with the error. This will avoid the duplication metadata.\n  if (error) return null\n  return metadata\n}\n"], "names": ["BrowserResolvedMetadata", "promise", "metadata", "error", "use"], "mappings": ";;;;+BAGgBA,2BAAAA;;;eAAAA;;;uBAHI;AAGb,SAASA,wBAAwB,KAIvC;IAJuC,IAAA,EACtCC,OAAO,EAGR,GAJuC;IAKtC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAE,GAAGC,CAAAA,GAAAA,OAAAA,GAAG,EAACH;IAChC,oEAAoE;IACpE,yFAAyF;IACzF,IAAIE,OAAO,OAAO;IAClB,OAAOD;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/shared/lib/server-inserted-metadata.shared-runtime.ts"], "sourcesContent": ["'use client'\n\nimport type React from 'react'\nimport { createContext } from 'react'\n\nexport type MetadataResolver = () => React.ReactNode\ntype MetadataResolverSetter = (m: MetadataResolver) => void\n\nexport const ServerInsertedMetadataContext =\n  createContext<MetadataResolverSetter | null>(null)\n"], "names": ["ServerInsertedMetadataContext", "createContext"], "mappings": "AAAA;;;;;+BAQaA,iCAAAA;;;eAAAA;;;uBALiB;AAKvB,MAAMA,gCACXC,CAAAA,GAAAA,OAAAA,aAAa,EAAgC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2182, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/client/components/metadata/server-inserted-metadata.tsx"], "sourcesContent": ["import { use, useContext } from 'react'\nimport {\n  type MetadataResolver,\n  ServerInsertedMetadataContext,\n} from '../../../shared/lib/server-inserted-metadata.shared-runtime'\nimport type { StreamingMetadataResolvedState } from './types'\n\n// Receives a metadata resolver setter from the context, and will pass the metadata resolving promise to\n// the context where we gonna use it to resolve the metadata, and render as string to append in <body>.\nconst useServerInsertedMetadata = (metadataResolver: MetadataResolver) => {\n  const setMetadataResolver = useContext(ServerInsertedMetadataContext)\n\n  if (setMetadataResolver) {\n    setMetadataResolver(metadataResolver)\n  }\n}\n\nexport function ServerInsertMetadata({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  // Apply use() to the metadata promise to suspend the rendering in SSR.\n  const { metadata } = use(promise)\n  // Insert metadata into the HTML stream through the `useServerInsertedMetadata`\n  useServerInsertedMetadata(() => metadata)\n\n  return null\n}\n"], "names": ["ServerInsertMetadata", "useServerInsertedMetadata", "metadataResolver", "setMetadataResolver", "useContext", "ServerInsertedMetadataContext", "promise", "metadata", "use"], "mappings": ";;;;+BAiBgBA,wBAAAA;;;eAAAA;;;uBAjBgB;qDAIzB;AAGP,wGAAwG;AACxG,uGAAuG;AACvG,MAAMC,4BAA4B,CAACC;IACjC,MAAMC,sBAAsBC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,qCAAAA,6BAA6B;IAEpE,IAAIF,qBAAqB;QACvBA,oBAAoBD;IACtB;AACF;AAEO,SAASF,qBAAqB,KAIpC;IAJoC,IAAA,EACnCM,OAAO,EAGR,GAJoC;IAKnC,uEAAuE;IACvE,MAAM,EAAEC,QAAQ,EAAE,GAAGC,CAAAA,GAAAA,OAAAA,GAAG,EAACF;IACzB,+EAA+E;IAC/EL;0DAA0B,IAAMM;;IAEhC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/client/components/metadata/async-metadata.tsx"], "sourcesContent": ["'use client'\n\nimport { Suspense, use } from 'react'\nimport type { StreamingMetadataResolvedState } from './types'\n\nexport const AsyncMetadata =\n  typeof window === 'undefined'\n    ? (\n        require('./server-inserted-metadata') as typeof import('./server-inserted-metadata')\n      ).ServerInsertMetadata\n    : (\n        require('./browser-resolved-metadata') as typeof import('./browser-resolved-metadata')\n      ).BrowserResolvedMetadata\n\nfunction MetadataOutlet({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  const { error, digest } = use(promise)\n  if (error) {\n    if (digest) {\n      // The error will lose its original digest after passing from server layer to client layer；\n      // We recover the digest property here to override the React created one if original digest exists.\n      ;(error as any).digest = digest\n    }\n    throw error\n  }\n  return null\n}\n\nexport function AsyncMetadataOutlet({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  return (\n    <Suspense fallback={null}>\n      <MetadataOutlet promise={promise} />\n    </Suspense>\n  )\n}\n"], "names": ["AsyncMetadata", "AsyncMetadataOutlet", "window", "require", "ServerInsertMetadata", "BrowserResolvedMetadata", "MetadataOutlet", "promise", "error", "digest", "use", "Suspense", "fallback"], "mappings": "AAAA;;;;;;;;;;;;;;;;IAKaA,aAAa,EAAA;eAAbA;;IA0BGC,mBAAmB,EAAA;eAAnBA;;;;uBA7Bc;AAGvB,MAAMD,gBACX,OAAOE,WAAW,cAEZC,QAAQ,qIACRC,oBAAoB,GAEpBD,QAAQ,sIACRE,uBAAuB;AAE/B,SAASC,eAAe,KAIvB;IAJuB,IAAA,EACtBC,OAAO,EAGR,GAJuB;IAKtB,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAE,GAAGC,CAAAA,GAAAA,OAAAA,GAAG,EAACH;IAC9B,IAAIC,OAAO;QACT,IAAIC,QAAQ;YACV,2FAA2F;YAC3F,mGAAmG;;YACjGD,MAAcC,MAAM,GAAGA;QAC3B;QACA,MAAMD;IACR;IACA,OAAO;AACT;AAEO,SAASP,oBAAoB,KAInC;IAJmC,IAAA,EAClCM,OAAO,EAGR,GAJmC;IAKlC,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACI,OAAAA,QAAQ,EAAA;QAACC,UAAU;kBAClB,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACN,gBAAAA;YAAeC,SAASA;;;AAG/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2285, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AM/GitHub/Time-Zone-Converter/time-zone-converter/node_modules/next/src/client/components/metadata/metadata-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../../lib/metadata/metadata-constants'\n\n// We use a namespace object to allow us to recover the name of the function\n// at runtime even when production bundling/minification is used.\nconst NameSpace = {\n  [METADATA_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n  [VIEWPORT_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n  [OUTLET_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n}\n\nexport const MetadataBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[METADATA_BOUNDARY_NAME.slice(0) as typeof METADATA_BOUNDARY_NAME]\n\nexport const ViewportBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[VIEWPORT_BOUNDARY_NAME.slice(0) as typeof VIEWPORT_BOUNDARY_NAME]\n\nexport const OutletBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[OUTLET_BOUNDARY_NAME.slice(0) as typeof OUTLET_BOUNDARY_NAME]\n"], "names": ["MetadataBoundary", "OutletBoundary", "ViewportBoundary", "NameSpace", "METADATA_BOUNDARY_NAME", "children", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "slice"], "mappings": "AAAA;;;;;;;;;;;;;;;;;IAkCaA,gBAAgB,EAAA;eAAhBA;;IAUAC,cAAc,EAAA;eAAdA;;IALAC,gBAAgB,EAAA;eAAhBA;;;mCAjCN;AAEP,4EAA4E;AAC5E,iEAAiE;AACjE,MAAMC,YAAY;IAChB,CAACC,mBAAAA,sBAAsB,CAAC,EAAE,SAAU,KAInC;QAJmC,IAAA,EAClCC,QAAQ,EAGT,GAJmC;QAKlC,OAAOA;IACT;IACA,CAACC,mBAAAA,sBAAsB,CAAC,EAAE,SAAU,KAInC;QAJmC,IAAA,EAClCD,QAAQ,EAGT,GAJmC;QAKlC,OAAOA;IACT;IACA,CAACE,mBAAAA,oBAAoB,CAAC,EAAE,SAAU,KAIjC;QAJiC,IAAA,EAChCF,QAAQ,EAGT,GAJiC;QAKhC,OAAOA;IACT;AACF;AAEO,MAAML,mBACX,AACA,4DAA4D,oBADoB;AAEhFG,SAAS,CAACC,mBAAAA,sBAAsB,CAACI,KAAK,CAAC,GAAoC;AAEtE,MAAMN,mBACX,AACA,4DAA4D,oBADoB;AAEhFC,SAAS,CAACG,mBAAAA,sBAAsB,CAACE,KAAK,CAAC,GAAoC;AAEtE,MAAMP,iBACX,AACA,4DAA4D,oBADoB;AAEhFE,SAAS,CAACI,mBAAAA,oBAAoB,CAACC,KAAK,CAAC,GAAkC", "ignoreList": [0], "debugId": null}}]}